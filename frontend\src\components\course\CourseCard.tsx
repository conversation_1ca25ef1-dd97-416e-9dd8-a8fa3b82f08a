import React from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { Course } from '@/components/chat/types/course';
import { COURSE_CARD_MESSAGES, COURSE_CARD_COLORS } from '@/components/course/utils/constants';
import { QUERY_TYPES } from '@/components/chat/utils/constants';

interface CourseCardProps {
  course: Course;
  queryType?: string;
}

const CourseCard: React.FC<CourseCardProps> = ({ course, queryType = '' }) => {
  const getBorderColor = () => {
    switch (queryType) {
      case QUERY_TYPES.SKILL:
        return COURSE_CARD_COLORS.SKILL;
      case QUERY_TYPES.ROLE:
        return COURSE_CARD_COLORS.ROLE;
      case QUERY_TYPES.TECHNOLOGY:
        return COURSE_CARD_COLORS.TECHNOLOGY;
      case QUERY_TYPES.COMBINED:
        return COURSE_CARD_COLORS.COMBINED;
      default:
        return '';
    }
  };

  return (
    <a href={course.url} target="_blank" rel="noopener noreferrer" className="block mb-4">
      <Card
        className={`hover:translate-x-1 transition-transform hover:shadow-lg ${
          queryType ? `border-l-4 ${getBorderColor()}` : ''
        }`}
      >
        <CardContent className="p-4">
          <div className="flex">
            {course.image && (
              <div className="mr-4 flex-shrink-0">
                <img
                  src={course.image}
                  alt={course.title}
                  className="w-20 h-20 object-cover rounded-md"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              </div>
            )}
            <div className="flex-grow">
              <h3 className="text-blue-800 text-lg font-semibold mb-2">{course.title}</h3>
              <p className="text-gray-600 mb-2">{course.description}</p>
            </div>
          </div>
          <div className="stats flex justify-between text-sm text-gray-500 mt-2">
            <span>
              ⭐ {course.rating}{' '}
              {course.rating_count
                ? `(${course.rating_count} ${COURSE_CARD_MESSAGES.STATS.RATINGS})`
                : ''}
            </span>
            <span className="font-medium">
              👥 {course.students} {COURSE_CARD_MESSAGES.STATS.LEARNERS}
            </span>
            <span>⏱️ {course.duration}</span>
          </div>
          <div className="price mt-2 text-right">
            <span
              className={`${course.price && course.price !== 'Free' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'} px-2 py-1 rounded-xl text-sm font-semibold`}
            >
              💰{' '}
              {course.price && course.price !== 'Free'
                ? COURSE_CARD_MESSAGES.PRICE.PAID
                : COURSE_CARD_MESSAGES.PRICE.FREE}
            </span>
          </div>
        </CardContent>
      </Card>
    </a>
  );
};

export default CourseCard;
