/* eslint-disable no-console */
import { useState } from 'react';
import {
  formatEnrollment,
  formatDurationInHours,
  formatCoursePrice,
} from '@/components/chat/utils/formatUtils';
import axiosInstance from '@/axios/axiosInstance';
import { CourseFromDB } from '@/components/chat/types/course';

export const useSearchCourses = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchCourses = async (query: string) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Searching for courses with query:', query);

      // Add "English" to the query to prioritize English courses
      const enhancedQuery = `${query} English`;
      console.log('Enhanced query with language preference:', enhancedQuery);

      // Use the vector search API exclusively to get courses from the database
      const response = await axiosInstance.post('/api/vector-search', { query: enhancedQuery });
      console.log('Vector search response:', response.data);

      if (response.data && response.data.courses && response.data.courses.length > 0) {
        console.log('Raw course data from API:', response.data.courses);

        // Log the first course to examine its structure in detail
        if (response.data.courses.length > 0) {
          console.log(
            'Detailed first course data:',
            JSON.stringify(response.data.courses[0], null, 2)
          );
          console.log('Price field:', response.data.courses[0].price);
          console.log('Product price field:', response.data.courses[0].product_price);
        }

        // Map the API response to the format expected by the CourseList component
        const dbCourses = response.data.courses.map((courseFromDB: CourseFromDB) => {
          // Format the learner count accurately using our helper function
          const formattedStudents = formatEnrollment(courseFromDB.enrollments);

          // Format the duration for display
          const formattedDuration = formatDurationInHours(courseFromDB.video_content_length);

          // Format the price from price_retail column
          const formattedPrice = formatCoursePrice(courseFromDB.price_retail);

          // Format rating to show one decimal place
          const rating = courseFromDB.rating_value
            ? parseFloat(courseFromDB.rating_value).toFixed(1) || '4.5'
            : '4.5';
          return {
            title: courseFromDB.product_name || 'Untitled Course',
            description: courseFromDB.product_subtitle || 'No description available',
            tags: [courseFromDB.category_primary || 'General'],
            rating: rating,
            rating_count: courseFromDB.rating_count || '',
            students: formattedStudents,
            duration: formattedDuration,
            url: courseFromDB.product_url || '#',
            price: formattedPrice,
            image: courseFromDB.product_image || '', // Add the course image
          };
        });

        console.log('Mapped database courses:', dbCourses);
        return dbCourses;
      }

      // If no courses found, return empty array - never use fallbacks
      console.log('No courses found in database for query:', query);
      return [];
    } catch (err) {
      console.error('Course search failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to search courses');
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  return {
    searchCourses,
    isLoading,
    error,
  };
};
