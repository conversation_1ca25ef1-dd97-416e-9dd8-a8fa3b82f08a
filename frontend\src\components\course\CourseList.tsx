import React, { useRef, useEffect } from 'react';

import { Course } from '@/components/chat/types/course';
import CourseCard from '@/components/course/CourseCard';
import CourseDefaultView from '@/components/course/CourseDefaultView';
import { COURSE_TITLES } from './utils/constants';
import { QUERY_TYPES } from '@/components/chat/utils/constants';

interface CourseListProps {
  courses?: Course[];
  showDefaultView?: boolean;
  queryType?: string; // 'skill', 'role', 'technology', or 'combined'
}

const CourseList: React.FC<CourseListProps> = ({
  courses = [],
  showDefaultView = true,
  queryType = '',
}) => {
  const courseListRef = useRef<HTMLDivElement>(null);

  // Scroll to top when courses change
  useEffect(() => {
    if (courseListRef.current && !showDefaultView) {
      courseListRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [courses, showDefaultView]); // Re-run when courses array or showDefaultView changes

  // If showDefaultView is true, we show the empty state with instructions
  // Otherwise, we show only the courses that were passed in from the database

  // Get a title based on the query type
  const getRecommendationTitle = () => {
    switch (queryType) {
      case QUERY_TYPES.SKILL:
        return COURSE_TITLES.SKILL_BASED;
      case QUERY_TYPES.ROLE:
        return COURSE_TITLES.CAREER_PATH;
      case QUERY_TYPES.TECHNOLOGY:
        return COURSE_TITLES.TECHNOLOGY;
      case QUERY_TYPES.COMBINED:
        return COURSE_TITLES.SPECIALIZED;
      default:
        return COURSE_TITLES.DEFAULT;
    }
  };

  return (
    <div className="w-[460px] bg-white rounded-lg shadow-lg p-4 flex flex-col">
      <div className="mb-2 px-2">
        <h2 className="text-blue-800 text-xl font-bold">{getRecommendationTitle()}</h2>
      </div>
      <div className="border-t border-gray-200 pt-4"></div>
      {showDefaultView ? (
        <CourseDefaultView />
      ) : (
        <div ref={courseListRef} className="flex-grow overflow-y-auto">
          {courses.map((course, index) => (
            <CourseCard key={index} course={course} queryType={queryType} />
          ))}
        </div>
      )}
    </div>
  );
};

export default CourseList;
