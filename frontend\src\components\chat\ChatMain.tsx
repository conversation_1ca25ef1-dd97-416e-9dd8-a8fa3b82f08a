/* eslint-disable no-console */
import { useState, useEffect } from 'react';
import { UserProfileDetails } from '@/components/user/types/user';
import CourseList from '@/components/course/CourseList';
import chatApi from '@/components/chat/api/chatApi';
import ChatHeader from '@/components/chat/ChatHeader';
import Chat<PERSON><PERSON> from '@/components/chat/ChatArea';
import {
  MessageType,
  ChatRole,
  Message,
  History,
  ChatResponse,
} from '@/components/chat/types/chat';
import {
  CHAT_STATIC_MESSAGES,
  ERROR_MESSAGES,
  UI_MESSAGES,
  QUERY_TYPES,
  QueryType,
  LOG_MESSAGES,
} from '@/components/chat/utils/constants';
import ChatInput from '@/components/chat/ChatInput';
import {
  isLearningQuery,
  defaultChatHistory,
  defaultMessages,
  buildSearchQueryFromIntent,
  IntentDetails,
} from '@/components/chat/utils/queryUtils';
import { useSkillDescription } from '@/components/chat/hooks/useSkillDescription';
import { useSearchCourses } from '@/components/chat/hooks/useSearchCourses';
import { Course } from '@/components/chat/types/course';

const ChatMain = () => {
  const { generateDescription } = useSkillDescription();
  const { searchCourses } = useSearchCourses();

  const [userProfile, setUserProfile] = useState<UserProfileDetails>({
    name: '',
    experience: '',
    interests: '',
    careerPath: '',
  });
  const [hasInteracted, setHasInteracted] = useState(false);
  const [isBackendConnected, setIsBackendConnected] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatHistory, setChatHistory] = useState<History[]>([]);
  // State to track if course recommendations are shown
  const [showCourseRecommendations, setShowCourseRecommendations] = useState(false);
  const [courseRecommendations, setCourseRecommendations] = useState<Course[]>([]);
  // TODO: Currently skillDescription is not used, but could be used for future reference
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const [skillDescription, setSkillDescription] = useState('');
  const [queryType, setQueryType] = useState<QueryType>(QUERY_TYPES.EMPTY);

  // add welcome messages
  useEffect(() => {
    if (messages.length === 0) {
      setMessages(defaultMessages);
      setChatHistory(defaultChatHistory);
    }
  }, [messages.length]);

  const saveUserDetails = (details: Partial<UserProfileDetails>) => {
    // Ensure all required fields are present by merging with current state
    const updatedProfile: UserProfileDetails = {
      name: details.name ?? userProfile.name,
      experience: details.experience ?? userProfile.experience,
      interests: details.interests ?? userProfile.interests,
      careerPath: details.careerPath ?? userProfile.careerPath,
    };

    if (!hasInteracted && details.name) {
      const personalizedMessage = CHAT_STATIC_MESSAGES.PERSONALIZED_WELCOME.replace(
        '{name}',
        details.name
      );
      setMessages([
        {
          text: personalizedMessage,
          type: 'bot' as MessageType,
        },
      ]);

      setChatHistory([
        {
          role: 'system' as ChatRole,
          content: CHAT_STATIC_MESSAGES.SYSTEM_INSTRUCTIONS,
        },
        { role: 'assistant' as ChatRole, content: personalizedMessage },
      ]);
    }
    setUserProfile(updatedProfile);
  };

  const handleReset = () => {
    setHasInteracted(false);
    // Reset course recommendations - ensure this happens immediately
    setShowCourseRecommendations(false);
    setCourseRecommendations([]);
    setSkillDescription('');
    setQueryType(QUERY_TYPES.EMPTY);

    // Reset user profile state
    setUserProfile({ name: '', experience: '', interests: '', careerPath: '' });

    // Set a consistent welcome message immediately to ensure UI updates
    setMessages(defaultMessages);
    // Reset chat history with improved system message
    setChatHistory(defaultChatHistory);
  };

  const handleSendMessage = async (userMessage: string) => {
    // Update messages state with user message and loading indicator in a single update
    setMessages((prevMessages) => [
      ...prevMessages,
      { text: userMessage, type: 'user' as MessageType },
      { text: '', type: 'loading' as MessageType },
    ]);

    // Mark that user has interacted with the chat
    setHasInteracted(true);

    if (!isBackendConnected) {
      // Fallback response if backend is not connected
      setTimeout(() => {
        appendMessageAndRemoveLoading(ERROR_MESSAGES.BACKEND_CONNECTION);
      }, 500);
      return;
    }

    try {
      // Process the query using the backend - start all API calls in parallel
      const [intentResponse, chatResponse] = await Promise.all([
        chatApi.processQuery(userMessage),
        chatApi.processChatMessage(userMessage, chatHistory),
      ]);
      // Extract intent details for skill/role descriptions
      const details = intentResponse.details;
      await updateQueryTypeAndDescription(details);

      // Search for relevant courses based on intent details and query context
      const isLearning = isLearningQuery(userMessage);

      try {
        // First, check if we have specific intent details (skill, role, technologies)
        const hasIntentDetails = details.skill || details.role || details.technologies;

        if (hasIntentDetails) {
          // Start the course search in parallel with other operations
          const intentCourses = await searchCoursesByIntent(details);

          if (intentCourses && intentCourses.length > 0) {
            console.log(LOG_MESSAGES.FOUND_INTENT_COURSES, intentCourses);
            setShowCourseRecommendations(true);
            setCourseRecommendations(intentCourses);

            // Set a combined query type if multiple intent details are present
            if (details.skill && (details.role || details.technologies)) {
              setQueryType(QUERY_TYPES.COMBINED);
            }
          } else if (isLearning) {
            // If no courses found with intent details but it's a learning query,
            // fall back to searching with the full user message
            console.log(LOG_MESSAGES.NO_INTENT_COURSES);
            await handleCourseSearch(userMessage, LOG_MESSAGES.FOUND_FALLBACK_COURSES);
          }
        } else if (isLearning) {
          // If no specific intent details but it's a learning query,
          // search using the full user message
          console.log(LOG_MESSAGES.SEARCHING_QUERY, userMessage);
          await handleCourseSearch(userMessage, LOG_MESSAGES.FOUND_VECTOR_COURSES);
        }
      } catch (error) {
        console.error(LOG_MESSAGES.FAILED_COURSE_RECOMMENDATIONS, error);
        // If there's an error, don't show the course recommendations panel
        setShowCourseRecommendations(false);
        setCourseRecommendations([]);
      }

      updateChatHistoryAndResponse(userMessage, chatResponse);
    } catch (error) {
      console.error(LOG_MESSAGES.ERROR_PROCESSING, error);
      // Add a simple error message and remove loading animation
      appendMessageAndRemoveLoading(ERROR_MESSAGES.PROCESSING_ERROR);
    }
  };

  const handleCourseSearch = async (userMessage: string, logMessage: string) => {
    const courses = await searchCourses(userMessage);
    if (courses && courses.length > 0) {
      console.log(logMessage, courses);
      setShowCourseRecommendations(true);
      setCourseRecommendations(courses);
    } else {
      console.log(LOG_MESSAGES.NO_COURSES);
      setShowCourseRecommendations(false);
      setCourseRecommendations([]);
    }
  };

  const searchCoursesByIntent = async (details: {
    skill?: string | null;
    role?: string | null;
    technologies?: string | null;
  }) => {
    console.log(LOG_MESSAGES.SEARCHING_INTENT, details);
    const searchQuery = buildSearchQueryFromIntent(details);
    if (!searchQuery) return [];

    // Use the existing search function with our constructed query
    const intentCourses = await searchCourses(searchQuery);
    return intentCourses;
  };

  const appendMessageAndRemoveLoading = (errorMessage: string) => {
    setMessages((prev) =>
      prev
        .filter((msg) => msg.type !== 'loading')
        .concat({
          text: errorMessage,
          type: 'bot' as MessageType,
        })
    );
  };

  const updateQueryTypeAndDescription = async (details: IntentDetails) => {
    let description = '';
    // Handle query type and description generation based on intent details
    if (details.skill) {
      setQueryType(QUERY_TYPES.SKILL);
      description = await generateDescription(details.skill);
    } else if (details.role) {
      setQueryType(QUERY_TYPES.ROLE);
      description = await generateDescription(details.role);
    } else if (details.technologies) {
      setQueryType(QUERY_TYPES.TECHNOLOGY);
      description = await generateDescription(details.technologies);
    } else {
      setQueryType(QUERY_TYPES.EMPTY);
      description = '';
    }

    // Add bot response to chat history
    setSkillDescription(description);
  };

  // Update chat history and handle bot response
  const updateChatHistoryAndResponse = (userMessage: string, chatResponse: ChatResponse) => {
    // Add user message to chat history
    const updatedChatHistory = [...chatHistory, { role: 'user' as ChatRole, content: userMessage }];

    // Get bot response with fallback
    let botResponse = chatResponse.response || CHAT_STATIC_MESSAGES.DEFAULT_RESPONSE;

    // Add course list note if relevant
    const shouldAddCourseNote =
      showCourseRecommendations &&
      courseRecommendations.length > 0 &&
      !botResponse.toLowerCase().includes('course list') &&
      !botResponse.toLowerCase().includes('recommendations');

    if (shouldAddCourseNote) {
      botResponse += UI_MESSAGES.COURSE_LIST_NOTE;
    }

    // Add bot response to chat history
    updatedChatHistory.push({ role: 'assistant' as ChatRole, content: botResponse });
    setChatHistory(updatedChatHistory);

    // Update UI messages
    appendMessageAndRemoveLoading(botResponse);
  };

  return (
    <div className="flex w-full max-w-[1400px] gap-4 h-[600px] mx-4 z-10">
      {/* Course recommendations panel */}
      <CourseList
        courses={courseRecommendations}
        showDefaultView={!showCourseRecommendations || courseRecommendations.length === 0}
        queryType={queryType}
      />
      {/* Chat panel */}
      <div className="flex-1 bg-white rounded-lg shadow-lg flex flex-col">
        <ChatHeader
          handleReset={handleReset}
          userProfile={userProfile}
          setIsBackendConnected={setIsBackendConnected}
          onSaveUserProfile={saveUserDetails}
        />

        <ChatArea messages={messages} isBackendConnected={isBackendConnected} />
        {/* Input area */}
        <ChatInput handleSendMessage={(userMessage: string) => handleSendMessage(userMessage)} />
      </div>
    </div>
  );
};

export default ChatMain;
