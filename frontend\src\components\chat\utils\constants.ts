export const LEARNING_KEYWORDS = {
  COURSE: 'course',
  LEARN: 'learn',
  STUDY: 'study',
  RECOMMEND: 'recommend',
  TRAINING: 'training',
} as const;

export const CHAT_STATIC_MESSAGES = {
  SYSTEM_DEFAULT:
    'You are a helpful AI learning assistant that helps users find relevant courses and learning resources.',

  SYSTEM_INSTRUCTIONS:
    'You are a helpful AI learning assistant that helps users find relevant courses and learning resources. You can help users find courses based on their skills, career goals, or specific technologies they want to learn.',

  ASSISTANT_DEFAULT: "Hello! I'm your AI learning assistant. How can I help you today?",

  PERSONALIZED_WELCOME: "Hello {name}! I'm your AI learning assistant. How can I help you today?",

  DEFAULT_RESPONSE: "I understand your query and I'm here to help.",
} as const;

export const ERROR_MESSAGES = {
  BACKEND_CONNECTION:
    "I'm currently having trouble connecting to my knowledge base. Please check if the server is running and refresh the page.",
  PROCESSING_ERROR:
    'I encountered an error processing your request. Please try again or rephrase your question.',
  BACKEND_ERROR: 'Backend connection error - Please restart the server',
  CHECKING_CONNECTION: 'Checking backend connection...',
  CONNECTION_SUCCESS: 'Backend connected successfully',
} as const;

export const UI_MESSAGES = {
  INPUT_PLACEHOLDER: 'Type your message here...',
  COURSE_LIST_NOTE:
    " I've found some relevant courses based on your interests. **Please check the course list panel on the left for detailed recommendations.**",
} as const;

export type QueryType = 'skill' | 'role' | 'technology' | 'combined' | '';

export const QUERY_TYPES = {
  SKILL: 'skill',
  ROLE: 'role',
  TECHNOLOGY: 'technology',
  COMBINED: 'combined',
  EMPTY: '',
} as const;

export const DESCRIPTION_MESSAGES = {
  FALLBACK: "{topic} is a valuable skill in today's job market.",
} as const;

export const LOG_MESSAGES = {
  RESET: 'Reset button clicked',
  SEARCHING_INTENT: 'Searching for courses based on detected intent details:',
  FOUND_INTENT_COURSES: 'Found courses from intent-based search:',
  NO_INTENT_COURSES: 'No courses found from intent search, trying full query search',
  FOUND_FALLBACK_COURSES: 'Found courses from fallback search:',
  NO_COURSES: 'No courses found from any search method',
  SEARCHING_QUERY: 'Searching for courses based on user query:',
  FOUND_VECTOR_COURSES: 'Found courses from vector search:',
  NO_VECTOR_COURSES: 'No courses found from vector search',
  BACKEND_CONNECTION_FAILED: 'Backend connection check failed:',
  FAILED_DESCRIPTION: 'Failed to generate description:',
  FAILED_COURSE_RECOMMENDATIONS: 'Failed to fetch course recommendations:',
  ERROR_PROCESSING: 'Error processing message:',
} as const;

export const CHAT_MESSAGES = {
  EMPTY_STATE: {
    CONNECTED: 'Type a message to start our conversation.',
    CONNECTING: 'Connecting to backend...',
    CHECK_SERVER: 'Please check if the backend server is running.',
  },
} as const;

export const MESSAGE_TYPES = {
  USER: 'user',
  BOT: 'bot',
  LOADING: 'loading',
} as const;

export const MESSAGE_CLASSES = {
  BASE: 'rounded-lg max-w-[95%] md:max-w-[80%] lg:max-w-[70%]',
  USER: 'p-2 md:p-3 bg-blue-500 text-white self-end text-sm md:text-base',
  LOADING: 'p-3 md:p-4 bg-blue-100 self-start',
  BOT: 'p-2 md:p-3 bg-gray-100 text-gray-800 self-start text-sm md:text-base',
} as const;

export const HEADER_MESSAGES = {
  TITLE: 'Mentoring Agent',
  SUBTITLE: 'Your AI-powered guide to career success',
  STATUS: {
    CONNECTED: 'Connected to course recommendation service',
    ERROR: 'Backend connection error - Please restart the server',
    RETRY: 'Retry',
  },
  BUTTONS: {
    RESET: 'Reset',
    USER_DETAILS: 'User Details',
  },
} as const;

export const CONNECTION_STATUS = {
  CHECKING: 'checking',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
} as const;

export type ConnectionStatus = (typeof CONNECTION_STATUS)[keyof typeof CONNECTION_STATUS];
