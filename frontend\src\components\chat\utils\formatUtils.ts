/* eslint-disable no-console */
export const formatStudentEnrollmentCount = (enrollments: number): string => {
  if (!enrollments) return '0';

  if (enrollments >= 1000000) {
    return `${(enrollments / 1000000).toFixed(1)}M`;
  } else if (enrollments >= 1000) {
    return `${(enrollments / 1000).toFixed(1)}K`;
  }
  return enrollments.toString();
};

export const formatDurationInHours = (minutes: number | null): string => {
  if (!minutes) return '0h';
  const hours = Math.round(minutes / 60);
  return `${hours}h`;
};

export const formatCoursePrice = (price: number | null): string => {
  if (!price) return 'Free';
  return `$${price.toFixed(2)}`;
};

/**
 * TODO: Remove this function once the API is updated to return the correct format
 * Helper function to parse and format enrollment numbers accurately
 * @param enrollmentValue The enrollment value from the API
 * @returns Formatted enrollment string
 */
export const formatEnrollment = (enrollmentValue: string | number | null): string => {
  console.log('formatEnrollment called with:', enrollmentValue, 'type:', typeof enrollmentValue);

  if (!enrollmentValue) {
    return '0';
  }

  // Convert to string and clean up
  let enrollmentStr = String(enrollmentValue).trim();

  // Handle strings that might contain "students" or other text
  enrollmentStr = enrollmentStr.replace(/[^0-9,.]/g, '');

  // Handle comma-separated numbers
  enrollmentStr = enrollmentStr.replace(/,/g, '');

  // Parse to number
  let enrollment = parseInt(enrollmentStr);

  // If parsing failed, return 0
  if (isNaN(enrollment)) {
    return '0';
  }

  // Format based on size
  if (enrollment >= 1000000) {
    // For millions, show one decimal if not a whole number
    const millions = enrollment / 1000000;
    return millions % 1 === 0 ? `${Math.floor(millions)}M` : `${millions.toFixed(1)}M`;
  } else if (enrollment >= 1000) {
    // For thousands, show one decimal if not a whole number
    const thousands = enrollment / 1000;
    return thousands % 1 === 0 ? `${Math.floor(thousands)}K` : `${thousands.toFixed(1)}K`;
  } else {
    // For small numbers, show as is
    return enrollment.toString();
  }
};

/**
 * TODO: Remove this function once the API is updated to return the correct format
 * Helper function to parse and format course price
 * @param priceValue The price value from the API
 * @returns Formatted price string
 */
export const formatPrice = (priceValue: string | number): string => {
  console.log('formatPrice called with:', priceValue, 'type:', typeof priceValue);

  if (!priceValue || priceValue === '0' || priceValue === 0) {
    console.log('Price is empty, zero, or falsy');
    return 'Free';
  }

  // For all other cases, return "Paid"
  console.log('Price is not empty or zero, returning "Paid"');
  return 'Paid';
};

/**
 * TODO: Remove this function once the API is updated to return the correct format
 * Formats course duration for display in hours
 * @param durationStr The duration string from the database
 * @returns A formatted duration string in hours
 */
export const formatDurationInMinutes = (durationStr: string | number): string => {
  // Return "varies" if duration is null or empty
  if (!durationStr) {
    return 'varies';
  }

  // Convert to string
  const duration = String(durationStr);

  // Handle HH:MM:SS format (e.g., "06:28:00")
  const timeMatch = duration.match(/^(\d+):(\d+):(\d+)$/);
  if (timeMatch) {
    const hours = parseInt(timeMatch[1]);
    const minutes = parseInt(timeMatch[2]);
    const totalHours = hours + minutes / 60;
    return `${totalHours.toFixed(1)} minutes`;
  }

  // Handle numeric values (treat as minutes)
  if (!isNaN(Number(duration))) {
    const minutes = Number(duration);
    const hours = minutes / 60;
    return `${hours.toFixed(1)} minutes`;
  }

  // Return the original value if we can't parse it
  return duration;
};
