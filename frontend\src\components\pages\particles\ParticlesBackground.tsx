import React, { useEffect, useMemo } from 'react';

interface ParticlesBackgroundProps {
  particleCount?: number;
  minSize?: number;
  maxSize?: number;
  animationDuration?: {
    min: number;
    max: number;
  };
  opacity?: number;
}

interface ParticleConfig {
  size: number;
  left: string;
  top: string;
  opacity: string;
  animation: string;
  animationDelay: string;
}

const ParticlesBackground: React.FC<ParticlesBackgroundProps> = ({
  particleCount = 30,
  minSize = 2,
  maxSize = 8,
  animationDuration = { min: 10, max: 30 },
  opacity = 0.2,
}) => {
  // Generate random float animation keyframes
  const floatAnimation = useMemo(() => {
    const generateRandomTransform = () => {
      const x = Math.random() * 100 - 50;
      const y = Math.random() * 100 - 50;
      return `translate(${x}px, ${y}px)`;
    };

    return `
      @keyframes float {
        0% { transform: translate(0, 0); }
        25% { transform: ${generateRandomTransform()}; }
        50% { transform: ${generateRandomTransform()}; }
        75% { transform: ${generateRandomTransform()}; }
        100% { transform: translate(0, 0); }
      }
    `;
  }, []);

  // Add animation styles to document
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = floatAnimation;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [floatAnimation]);

  // Generate particle configurations
  const particles = useMemo(() => {
    const generateParticleConfig = (): ParticleConfig => {
      const size = Math.random() * (maxSize - minSize) + minSize;
      const duration =
        Math.random() * (animationDuration.max - animationDuration.min) + animationDuration.min;
      const delay = Math.random() * 5;

      return {
        size,
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        opacity: opacity.toString(),
        animation: `float ${duration}s linear infinite`,
        animationDelay: `${delay}s`,
      };
    };

    return Array.from({ length: particleCount }, generateParticleConfig);
  }, [particleCount, minSize, maxSize, animationDuration, opacity]);

  return (
    <div id="particles-container" className="absolute inset-0 z-0" aria-hidden="true">
      {particles.map((config, index) => (
        <div
          key={`particle-${index}`}
          className="absolute rounded-full bg-white"
          style={{
            width: `${config.size}px`,
            height: `${config.size}px`,
            left: config.left,
            top: config.top,
            opacity: config.opacity,
            animation: config.animation,
            animationDelay: config.animationDelay,
            willChange: 'transform',
          }}
        />
      ))}
    </div>
  );
};

export default React.memo(ParticlesBackground);
