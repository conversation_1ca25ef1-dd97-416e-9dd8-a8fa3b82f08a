import { useState, useEffect, useRef } from 'react';
import CourseList from './CourseList';
import ChatSection from './ChatSection';
import UserProfile from './UserProfile';
import apiService from '../services/api';

const MainPage = () => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [userProfile, setUserProfile] = useState({
    name: '',
    experience: '',
    interests: '',
    careerPath: ''
  });
  const [hasInteracted, setHasInteracted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<{text: string, type: 'user' | 'bot' | 'loading'}[]>([]);
  const [chatHistory, setChatHistory] = useState<{role: string, content: string}[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [backendStatus, setBackendStatus] = useState<{
    connected: boolean;
    message: string;
  }>({
    connected: false,
    message: 'Checking backend connection...'
  });

  // Reference to the chat messages container for auto-scrolling
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Function to check backend connection
  const checkBackendConnection = async () => {
    try {
      setBackendStatus({
        ...backendStatus,
        message: 'Checking backend connection...'
      });

      const healthStatus = await apiService.checkHealth();

      if (healthStatus.database && healthStatus.openai) {
        setBackendStatus({
          connected: true,
          message: 'Backend connected successfully'
        });
      } else {
        setBackendStatus({
          connected: false,
          message: 'Backend connection error - Please restart the server'
        });
      }
    } catch (error) {
      console.error('Backend connection check failed:', error);
      setBackendStatus({
        connected: false,
        message: 'Backend connection error - Please restart the server'
      });
    }
  };

  // Check backend connection on component mount and add welcome message
  useEffect(() => {
    checkBackendConnection();

    // Add initial welcome message when the component mounts - use a consistent message
    if (messages.length === 0) {
      // Set a consistent welcome message immediately
      setMessages([{
        text: "Hello! I'm your AI-powered career guide. I can help you find courses and resources based on your interests. What would you like to learn about today?",
        type: 'bot'
      }]);

      // Set up the chat history with improved system message
      setChatHistory([
        { role: "system", content: "You are a helpful AI mentoring agent that assists users in finding Udemy courses. Focus exclusively on Udemy as the learning platform and only recommend English courses. When responding to users: 1) Use proper formatting with markdown - use **bold** for emphasis and proper line breaks for lists, 2) Include personalized learning tips relevant to the user's query, 3) Make responses conversational and dynamic, not formulaic, 4) Avoid mentioning other learning platforms like Coursera, edX, etc., 5) Structure your responses clearly with proper paragraphs, 6) Provide effective learning strategies specific to the user's interests, 7) DO NOT include specific course details in your messages - the course list will be displayed separately, 8) Only explain about skills/roles/technologies based on user's prompt in the chat container, not course recommendations." },
        { role: "assistant", content: "Hello! I'm your AI-powered career guide. I can help you find courses and resources based on your interests. What would you like to learn about today?" }
      ]);
    }
  }, []);

  // Auto-scroll to the latest message when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      // Scroll to the bottom of the chat container with smooth animation
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [messages]); // Re-run when messages change

  // Create floating particles effect
  useEffect(() => {
    const createParticles = () => {
      const container = document.getElementById('particles-container');
      if (container) {
        // Clear existing particles
        container.innerHTML = '';

        // Create new particles
        for (let i = 0; i < 30; i++) {
          const particle = document.createElement('div');
          const size = Math.random() * 6 + 2; // Random size between 2-8px

          particle.className = 'absolute rounded-full bg-white opacity-20';
          particle.style.width = `${size}px`;
          particle.style.height = `${size}px`;
          particle.style.left = `${Math.random() * 100}%`;
          particle.style.top = `${Math.random() * 100}%`;
          particle.style.animation = `float ${Math.random() * 20 + 10}s linear infinite`;
          particle.style.animationDelay = `${Math.random() * 5}s`;

          container.appendChild(particle);
        }
      }
    };

    createParticles();

    // Add CSS animation for floating particles
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes float {
        0% { transform: translate(0, 0); }
        25% { transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px); }
        50% { transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px); }
        75% { transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px); }
        100% { transform: translate(0, 0); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  // Function to generate descriptions for skills, roles, or technologies
  const generateSkillDescription = async (topic: string): Promise<string> => {
    try {
      // Use the OpenAI API to generate a concise description
      const response = await apiService.generateDescription(topic);
      return response.description || `${topic} is a valuable skill in today's job market.`;
    } catch (error) {
      console.error('Failed to generate description:', error);
      return `${topic} is a valuable skill in today's job market.`;
    }
  };

  const saveUserDetails = (details: any) => {
    // Check if we should close the panel
    if (details.closePanel) {
      setSidebarActive(false);
      return;
    }

    // If details is an empty object, just toggle the sidebar
    if (Object.keys(details).length === 0) {
      setSidebarActive(false);
      return;
    }

    // Check if this is the first interaction and we have valid details
    if (!hasInteracted && details.name) {
      // Add a personalized welcome message
      const personalizedMessage = `Hello ${details.name}! I'm your AI-powered career guide. I can help you find courses and resources based on your interests. What would you like to learn about today?`;

      // Set the personalized message
      setMessages([{
        text: personalizedMessage,
        type: 'bot'
      }]);

      // Update chat history
      setChatHistory([
        { role: "system", content: "You are a helpful AI mentoring agent that assists users in finding courses and learning resources. Be friendly and personalized in your responses." },
        { role: "assistant", content: personalizedMessage }
      ]);
    }

    // Save the user profile
    setUserProfile(details);
  };

  // State to track if course recommendations are shown
  const [showCourseRecommendations, setShowCourseRecommendations] = useState(false);
  const [courseRecommendations, setCourseRecommendations] = useState<any[]>([]);
  const [skillDescription, setSkillDescription] = useState('');
  const [queryType, setQueryType] = useState('');
  const [isLoadingCourses, setIsLoadingCourses] = useState(false);

  const handleReset = () => {
    console.log("Reset button clicked");

    // Reset input
    setInputValue('');

    // Reset interaction state
    setHasInteracted(false);

    // Reset course recommendations - ensure this happens immediately
    setShowCourseRecommendations(false);
    setCourseRecommendations([]);
    setSkillDescription('');
    setQueryType('');

    // Reset loading state if active
    setIsLoadingCourses(false);

    // Reset user profile state
    setUserProfile({
      name: '',
      experience: '',
      interests: '',
      careerPath: ''
    });

    // Set a consistent welcome message immediately to ensure UI updates
    setMessages([{
      text: "Hello! I'm your AI-powered career guide. I can help you find courses and resources based on your interests. What would you like to learn about today?",
      type: 'bot' as const
    }]);

    // Reset chat history with improved system message
    setChatHistory([
      { role: "system", content: "You are a helpful AI mentoring agent that assists users in finding Udemy courses. Focus exclusively on Udemy as the learning platform and only recommend English courses. When responding to users: 1) Use proper formatting with markdown - use **bold** for emphasis and proper line breaks for lists, 2) Include personalized learning tips relevant to the user's query, 3) Make responses conversational and dynamic, not formulaic, 4) Avoid mentioning other learning platforms like Coursera, edX, etc., 5) Structure your responses clearly with proper paragraphs, 6) Provide effective learning strategies specific to the user's interests, 7) DO NOT include specific course details in your messages - the course list will be displayed separately, 8) Only explain about skills/roles/technologies based on user's prompt in the chat container, not course recommendations." },
      { role: "assistant", content: "Hello! I'm your AI-powered career guide. I can help you find courses and resources based on your interests. What would you like to learn about today?" }
    ]);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Store the user's message
    const userMessage = inputValue.trim();

    // Add user message to the UI
    const newMessages = [...messages, { text: userMessage, type: 'user' as const }];
    setMessages(newMessages);

    // Mark that user has interacted
    setHasInteracted(true);

    // Clear input
    setInputValue('');

    // Show loading animation immediately
    setMessages(prev => [...prev, { text: '', type: 'loading' as const }]);
    setIsLoading(true);

    try {
      if (backendStatus.connected) {
        // Process the query using the backend - start all API calls in parallel
        const intentPromise = apiService.processQuery(userMessage);
        const chatPromise = apiService.processChatMessage(userMessage, chatHistory);

        // Get the intent response first
        const intentResponse = await intentPromise;

        // Extract intent details for skill/role descriptions
        const category = intentResponse.category;
        const details = intentResponse.details;

        // Determine query type and generate description
        if (details.skill) {
          setQueryType('skill');
          // Generate a description for the skill
          const skillDesc = await generateSkillDescription(details.skill);
          setSkillDescription(skillDesc);
        } else if (details.role) {
          setQueryType('role');
          // Generate a description for the role
          const roleDesc = await generateSkillDescription(details.role);
          setSkillDescription(roleDesc);
        } else if (details.technologies) {
          setQueryType('technology');
          // Generate a description for the technology
          const techDesc = await generateSkillDescription(details.technologies);
          setSkillDescription(techDesc);
        } else {
          // Reset if no specific skill/role/technology
          setQueryType('');
          setSkillDescription('');
        }

        // Search for relevant courses based on intent details and query context
        const isLearningQuery = userMessage.toLowerCase().includes('course') ||
            userMessage.toLowerCase().includes('learn') ||
            userMessage.toLowerCase().includes('study') ||
            userMessage.toLowerCase().includes('recommend') ||
            userMessage.toLowerCase().includes('training');

        try {
          // First, check if we have specific intent details (skill, role, technologies)
          const hasIntentDetails = details.skill || details.role || details.technologies;

          if (hasIntentDetails) {
            console.log('Searching for courses based on detected intent details:', details);

            // Log the category and confidence information
            console.log('Intent category:', category);
            console.log('Combined flag:', details.combined);
            console.log('Confidence scores:', details.confidence);

            // Start the course search in parallel with other operations
            const courseSearchPromise = apiService.searchCoursesByIntent({
              skill: details.skill,
              role: details.role,
              technologies: details.technologies,
              combined: details.combined,
              confidence: details.confidence
            });

            // Continue with other operations and await the result later
            const intentCourses = await courseSearchPromise;

            if (intentCourses && intentCourses.length > 0) {
              console.log('Found courses from intent-based search:', intentCourses);
              setShowCourseRecommendations(true);
              setCourseRecommendations(intentCourses);

              // Set query type based on the category from the backend
              if (details.combined) {
                setQueryType('combined');
              } else if (category.includes('skill')) {
                setQueryType('skill');
              } else if (category.includes('role')) {
                setQueryType('role');
              } else if (category.includes('technology')) {
                setQueryType('technology');
              }
            } else if (isLearningQuery) {
              // If no courses found with intent details but it's a learning query,
              // fall back to searching with the full user message
              console.log('No courses found from intent search, trying full query search');
              const fallbackSearchPromise = apiService.searchCourses(userMessage);

              // Continue with other operations and await the result later
              const fallbackCourses = await fallbackSearchPromise;

              if (fallbackCourses && fallbackCourses.length > 0) {
                console.log('Found courses from fallback search:', fallbackCourses);
                setShowCourseRecommendations(true);
                setCourseRecommendations(fallbackCourses);
              } else {
                console.log('No courses found from any search method');
                setShowCourseRecommendations(false);
                setCourseRecommendations([]);
              }
            }
          } else if (isLearningQuery) {
            // If no specific intent details but it's a learning query,
            // search using the full user message
            console.log('Searching for courses based on user query:', userMessage);
            const directSearchPromise = apiService.searchCourses(userMessage);

            // Continue with other operations and await the result later
            const courses = await directSearchPromise;

            if (courses && courses.length > 0) {
              console.log('Found courses from vector search:', courses);
              setShowCourseRecommendations(true);
              setCourseRecommendations(courses);
            } else {
              console.log('No courses found from vector search');
              setShowCourseRecommendations(false);
              setCourseRecommendations([]);
            }
          }
        } catch (error) {
          console.error('Failed to fetch course recommendations:', error);
          // If there's an error, don't show the course recommendations panel
          setShowCourseRecommendations(false);
          setCourseRecommendations([]);
        }

        // Update chat history with the new user message
        const updatedChatHistory = [
          ...chatHistory,
          { role: "user", content: userMessage }
        ];

        // Get the chat response from the already started promise
        const chatResponse = await chatPromise;

        // Extract the bot response
        let botResponse = chatResponse.response || "I understand your query and I'm here to help.";

        // For course-related queries, add a note about the course list if courses were found
        if (showCourseRecommendations && courseRecommendations.length > 0) {
          // Only add this if the response doesn't already mention checking the course list
          if (!botResponse.toLowerCase().includes('course list') &&
              !botResponse.toLowerCase().includes('recommendations')) {
            botResponse += " I've found some relevant courses based on your interests. **Please check the course list panel on the left for detailed recommendations.**";
          }
        }

        // Update chat history with the bot's response
        updatedChatHistory.push({ role: "assistant", content: botResponse });
        setChatHistory(updatedChatHistory);

        // Remove loading message and add bot response to UI
        setMessages(prev => prev.filter(msg => msg.type !== 'loading').concat({
          text: botResponse,
          type: 'bot' as const
        }));

        // Reset loading state
        setIsLoading(false);
      } else {
        // Fallback response if backend is not connected - keep it simple
        setTimeout(() => {
          setMessages(prev => prev.filter(msg => msg.type !== 'loading').concat({
            text: "I'm currently having trouble connecting to my knowledge base. Please check if the server is running and refresh the page.",
            type: 'bot' as const
          }));
          setIsLoading(false);
        }, 1000);
      }
    } catch (error) {
      console.error('Error processing message:', error);
      // Add a simple error message and remove loading animation
      setMessages(prev => prev.filter(msg => msg.type !== 'loading').concat({
        text: "I encountered an error processing your request. Please try again or rephrase your question.",
        type: 'bot' as const
      }));
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-blue-900 flex justify-center items-center p-0 relative overflow-hidden">
      {/* Particles container */}
      <div id="particles-container" className="absolute inset-0 z-0"></div>

      {/* Left side decorative elements */}
      <div className="absolute left-0 top-0 h-full w-[15%] flex flex-col justify-center items-center">
        <div className="w-24 h-24 rounded-full bg-white opacity-10 mb-8"></div>
        <div className="w-16 h-16 rounded-full bg-white opacity-10 mb-8"></div>
        <div className="w-20 h-20 rounded-full bg-white opacity-10"></div>

        {/* Left side decorative lines */}
        <div className="absolute top-[20%] left-[30%] w-[150px] h-[1px] bg-white opacity-20 transform -rotate-45"></div>
        <div className="absolute top-[40%] left-[20%] w-[100px] h-[1px] bg-white opacity-20 transform rotate-30"></div>
        <div className="absolute top-[70%] left-[40%] w-[120px] h-[1px] bg-white opacity-20 transform rotate-15"></div>
      </div>

      {/* Right side decorative elements */}
      <div className="absolute right-0 top-0 h-full w-[15%] flex flex-col justify-center items-center">
        <div className="w-20 h-20 rounded-full bg-white opacity-10 mb-8"></div>
        <div className="w-24 h-24 rounded-full bg-white opacity-10 mb-8"></div>
        <div className="w-16 h-16 rounded-full bg-white opacity-10"></div>

        {/* Right side decorative lines */}
        <div className="absolute top-[25%] right-[30%] w-[150px] h-[1px] bg-white opacity-20 transform rotate-45"></div>
        <div className="absolute top-[45%] right-[20%] w-[100px] h-[1px] bg-white opacity-20 transform -rotate-30"></div>
        <div className="absolute top-[75%] right-[40%] w-[120px] h-[1px] bg-white opacity-20 transform -rotate-15"></div>
      </div>

      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/50 to-blue-800/50 z-0"></div>

      <div className="flex w-full max-w-[1400px] gap-4 h-[600px] mx-4 z-10">
        {/* Course recommendations panel */}
        <CourseList
          courses={courseRecommendations}
          showDefaultView={!showCourseRecommendations || courseRecommendations.length === 0}
          skillDescription={skillDescription}
          queryType={queryType}
        />

        {/* Chat panel */}
        <div className="flex-1 bg-white rounded-lg shadow-lg flex flex-col">
          {/* Header */}
          <div className="py-2 px-4 border-b border-gray-200 flex flex-col items-center">
            {/* Title and subtitle centered */}
            <div className="text-center w-full mb-1">
              <h1 className="text-2xl font-bold text-blue-800">Mentoring Agent</h1>
              <p className="text-gray-600 text-sm">Your AI-powered guide to career success</p>
            </div>

            {/* Backend status centered */}
            <div className="flex justify-center items-center w-full mb-2">
              <p className={`${backendStatus.connected ? 'text-green-500' : 'text-red-500'} text-xs`}>
                <i className={`fas fa-circle text-xs mr-1 ${backendStatus.connected ? 'text-green-500' : 'text-red-500'}`}></i>
                {backendStatus.connected ? 'Connected to course recommendation service' : 'Backend connection error - Please restart the server'}
              </p>
              {!backendStatus.connected && (
                <button
                  onClick={checkBackendConnection}
                  className="text-xs text-blue-500 hover:text-blue-700 underline ml-2"
                >
                  Retry
                </button>
              )}
            </div>

            {/* Action buttons row */}
            <div className="flex justify-end items-center w-full">
              {/* Action buttons */}
              <div className="flex gap-2">
                <button
                  onClick={handleReset}
                  className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-sm hover:bg-gray-300 transition-colors"
                >
                  Reset
                </button>
                <button
                  onClick={toggleSidebar}
                  className="px-3 py-1 bg-blue-800 text-white rounded-md text-sm flex items-center gap-1"
                >
                  User Details
                </button>
              </div>
            </div>
          </div>

          {/* Chat area */}
          <div
            ref={chatContainerRef}
            className="flex-grow px-6 py-6 overflow-y-auto scroll-smooth relative"
          >
            {messages.length > 0 ? (
              <div className="flex flex-col space-y-4">
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={`rounded-lg ${
                      message.type === 'user'
                        ? 'p-3 bg-blue-800 text-white self-end'
                        : message.type === 'loading'
                          ? 'p-4 bg-blue-100 self-start'
                          : 'p-3 bg-gray-100 text-gray-800 self-start'
                    } max-w-[80%]`}
                  >
                    {message.type === 'loading'
                      ? <div className="flex items-center space-x-3">
                          <div className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDuration: '0.6s' }}></div>
                          <div className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDuration: '0.6s', animationDelay: '0.2s' }}></div>
                          <div className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDuration: '0.6s', animationDelay: '0.4s' }}></div>
                        </div>
                      : message.type === 'bot'
                        ? <div
                            className="whitespace-pre-line"
                            dangerouslySetInnerHTML={{
                              __html: message.text
                                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                                .replace(/\n/g, '<br />')
                            }}
                          />
                        : message.text
                    }
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-full flex flex-col items-center justify-center text-gray-600">
                {backendStatus.connected ? (
                  <>
                    <p className="mb-2 text-center">Type a message to start our conversation.</p>
                  </>
                ) : (
                  <>
                    <p className="mb-2 text-center">Connecting to backend...</p>
                    <p className="text-center">Please check if the backend server is running.</p>
                  </>
                )}
              </div>
            )}


          </div>

          {/* Input area */}
          <div className="p-3 border-t border-gray-200 flex gap-2">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type your message here..."
              className="flex-grow p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
            />
            <button
              onClick={handleSendMessage}
              className="px-4 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-700"
            >
              Send
            </button>
          </div>
        </div>
      </div>

      {/* User profile sidebar */}
      <UserProfile
        isActive={sidebarActive}
        onSave={saveUserDetails}
      />
    </div>
  );
};

export default MainPage;
