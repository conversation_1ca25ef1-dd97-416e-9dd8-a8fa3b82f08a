import { FC, useState } from 'react';
import { Button } from '@/components/ui/button';
import { UI_MESSAGES } from '@/components/chat/utils/constants';

interface ChatInputProps {
  handleSendMessage: (userMessage: string) => Promise<void>;
}

const ChatInput: FC<ChatInputProps> = ({ handleSendMessage }) => {
  const [inputValue, setInputValue] = useState('');
  const sendButtonClikHandler = async () => {
    if (!inputValue.trim()) return;
    // Store the user's message
    const userMessage = inputValue.trim();
    setInputValue('');
    handleSendMessage(userMessage);
  };
  return (
    <div className="p-3 border-t border-gray-200 flex">
      <input
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={(e) => e.key === 'Enter' && sendButtonClikHandler()}
        placeholder={UI_MESSAGES.INPUT_PLACEHOLDER}
        className="w-full min-w-[100px] p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 mr-2"
      />
      <Button onClick={sendButtonClikHandler}>Send</Button>
    </div>
  );
};

export default ChatInput;
