/* eslint-disable no-console */
import axiosInstance from '@/axios/axiosInstance';
import {
  formatEnrollment,
  formatCoursePrice,
  formatDurationInHours,
} from '@/components/chat/utils/formatUtils';
import { History } from '@/components/chat/types/chat';
import { CourseFromDB } from '@/components/chat/types/course';

// API endpoints
export const chatApi = {
  // Process user query for intent analysis
  processQuery: async (query: string) => {
    try {
      const response = await axiosInstance.post('/api/openai-intent', { query });
      return response.data;
    } catch (error) {
      console.error('Query processing failed:', error);
      throw error;
    }
  },

  // Process chat message with history
  processChatMessage: async (query: string, chatHistory: History[]) => {
    try {
      const response = await axiosInstance.post('/api/chat', { query, chatHistory });
      return response.data;
    } catch (error) {
      console.error('Chat processing failed:', error);
      throw error;
    }
  },

  // TODO: Evaluate if this method is still needed in the codebase. If not being used by any components, remove it to reduce technical debt.
  // Get all courses with pagination
  getCourses: async (limit = 100, offset = 0) => {
    try {
      console.log(`Fetching courses with limit=${limit}, offset=${offset}`);
      const response = await axiosInstance.get('/courses', {
        params: { limit, offset },
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch courses:', error);
      // Return empty array instead of throwing
      return [];
    }
  },

  // TODO: Evaluate if getTopCourses still needs this method - currently only used for fetching course data in getTopCourses
  // Get top courses (sorted by enrollments)
  getTopCourses: async (limit = 3, category = '') => {
    try {
      console.log(`Getting top ${limit} courses in category: ${category || 'all'}`);

      // Get a limited set of courses to avoid memory issues
      const courses = await chatApi.getCourses(100, 0);

      if (!courses || courses.length === 0) {
        console.log('No courses returned from API');
        return [];
      }

      // Filter courses with necessary fields and ensure they're English
      let validCourses = courses.filter((course: CourseFromDB) => {
        // Check if the course has a name and is in English
        return (
          course.product_name &&
          (course.course_language === 'en' || course.course_language === null)
        );
      });

      console.log(`Found ${validCourses.length} valid English courses`);

      // Apply category filter if provided
      if (category) {
        const categoryLower = category.toLowerCase();
        validCourses = validCourses.filter((course: CourseFromDB) => {
          const primaryCategory = course.category_primary
            ? course.category_primary.toLowerCase()
            : '';
          return primaryCategory.includes(categoryLower);
        });
        console.log(`After category filter: ${validCourses.length} courses`);
      }

      // Sort by enrollments (if available)
      validCourses.sort((a: CourseFromDB, b: CourseFromDB) => {
        const enrollmentsA = a.enrollments || 0;
        const enrollmentsB = b.enrollments || 0;
        return enrollmentsB - enrollmentsA;
      });

      // Return top N courses in the expected format
      const topCourses = validCourses.slice(0, limit).map((course: CourseFromDB) => {
        // Format the learner count accurately using our helper function
        const formattedStudents = formatEnrollment(course.enrollments);

        // Format the duration for display
        const formattedDuration = formatDurationInHours(course.video_content_length);

        // Format the price from price_retail column
        const formattedPrice = formatCoursePrice(course.price_retail);

        // Format rating to show one decimal place
        const rating = course.rating_value
          ? parseFloat(course.rating_value).toFixed(1) || '4.5'
          : '4.5';

        return {
          title: course.product_name || 'Untitled Course',
          description: course.product_subtitle || 'No description available',
          tags: [course.category_primary || 'General'],
          rating: rating,
          rating_count: course.rating_count || '',
          students: formattedStudents,
          duration: formattedDuration,
          url: course.product_url || '#',
          price: formattedPrice,
          image: course.product_image || '', // Add the course image
        };
      });

      console.log(`Returning ${topCourses.length} top courses`);
      return topCourses;
    } catch (error) {
      console.error('Failed to fetch top courses:', error);
      // Return empty array instead of fallbacks
      return [];
    }
  },

  // TODO: Remove getFallbackCourses method since we now exclusively use database courses
  // No fallback courses - we only use database courses
  getFallbackCourses: (_query: string) => {
    console.log('Fallback courses disabled - only using database courses.', _query);
    return [];
  },
};

export default chatApi;
