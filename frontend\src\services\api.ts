import axios from 'axios';

/**
 * Helper function to parse and format enrollment numbers accurately
 * @param enrollmentValue The enrollment value from the API
 * @returns Formatted enrollment string
 */
const formatEnrollment = (enrollmentValue: any): string => {
  console.log('formatEnrollment called with:', enrollmentValue, 'type:', typeof enrollmentValue);

  if (!enrollmentValue) {
    return '0';
  }

  // Convert to string and clean up
  let enrollmentStr = String(enrollmentValue).trim();

  // Handle strings that might contain "students" or other text
  enrollmentStr = enrollmentStr.replace(/[^0-9,.]/g, '');

  // Handle comma-separated numbers
  enrollmentStr = enrollmentStr.replace(/,/g, '');

  // Parse to number
  let enrollment = parseInt(enrollmentStr);

  // If parsing failed, return 0
  if (isNaN(enrollment)) {
    return '0';
  }

  // Format based on size
  if (enrollment >= 1000000) {
    // For millions, show one decimal if not a whole number
    const millions = enrollment / 1000000;
    return millions % 1 === 0 ? `${Math.floor(millions)}M` : `${millions.toFixed(1)}M`;
  } else if (enrollment >= 1000) {
    // For thousands, show one decimal if not a whole number
    const thousands = enrollment / 1000;
    return thousands % 1 === 0 ? `${Math.floor(thousands)}K` : `${thousands.toFixed(1)}K`;
  } else {
    // For small numbers, show as is
    return enrollment.toString();
  }
};

/**
 * Helper function to parse and format course price
 * @param priceValue The price value from the API
 * @returns Formatted price string
 */
const formatPrice = (priceValue: any): string => {
  console.log('formatPrice called with:', priceValue, 'type:', typeof priceValue);

  if (!priceValue || priceValue === '0' || priceValue === 0) {
    console.log('Price is empty, zero, or falsy');
    return 'Free';
  }

  // For all other cases, return "Paid"
  console.log('Price is not empty or zero, returning "Paid"');
  return 'Paid';
};

/**
 * Formats course duration for display in hours
 * @param durationStr The duration string from the database
 * @returns A formatted duration string in hours
 */
const formatDurationInHours = (durationStr: any): string => {
  // Return "varies" if duration is null or empty
  if (!durationStr) {
    return 'varies';
  }

  // Convert to string
  const duration = String(durationStr);

  // Handle HH:MM:SS format (e.g., "06:28:00")
  const timeMatch = duration.match(/^(\d+):(\d+):(\d+)$/);
  if (timeMatch) {
    const hours = parseInt(timeMatch[1]);
    const minutes = parseInt(timeMatch[2]);
    const totalHours = hours + (minutes / 60);
    return `${totalHours.toFixed(1)} minutes`;
  }

  // Handle numeric values (treat as minutes)
  if (!isNaN(Number(duration))) {
    const minutes = Number(duration);
    const hours = minutes / 60;
    return `${hours.toFixed(1)} minutes`;
  }

  // Return the original value if we can't parse it
  return duration;
};

// Create an axios instance with default config
const api = axios.create({
  baseURL: 'http://localhost:3000', // Backend FastAPI server URL
  timeout: 30000, // Increased timeout to 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// API endpoints
export const apiService = {
  // Health check endpoint
  checkHealth: async () => {
    try {
      const response = await api.get('/health');
      return response.data;
    } catch (error) {
      console.error('Health check failed:', error);
      return { status: 'error', message: 'Backend connection failed' };
    }
  },

  // Get all courses with pagination
  getCourses: async (limit = 100, offset = 0) => {
    try {
      console.log(`Fetching courses with limit=${limit}, offset=${offset}`);
      const response = await api.get('/courses', {
        params: { limit, offset }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch courses:', error);
      // Return empty array instead of throwing
      return [];
    }
  },

  // Get top courses (sorted by enrollments)
  getTopCourses: async (limit = 3, category = '') => {
    try {
      console.log(`Getting top ${limit} courses in category: ${category || 'all'}`);

      // Get a limited set of courses to avoid memory issues
      const courses = await apiService.getCourses(100, 0);

      if (!courses || courses.length === 0) {
        console.log('No courses returned from API');
        return [];
      }

      // Filter courses with necessary fields and ensure they're English
      let validCourses = courses.filter((course: any) => {
        // Check if the course has a name and is in English
        return course.product_name &&
               (course.course_language === 'en' || course.course_language === null);
      });

      console.log(`Found ${validCourses.length} valid English courses`);

      // Apply category filter if provided
      if (category) {
        const categoryLower = category.toLowerCase();
        validCourses = validCourses.filter((course: any) => {
          const primaryCategory = course.category_primary ? course.category_primary.toLowerCase() : '';
          return primaryCategory.includes(categoryLower);
        });
        console.log(`After category filter: ${validCourses.length} courses`);
      }

      // Sort by enrollments (if available)
      validCourses.sort((a: any, b: any) => {
        const enrollmentsA = parseInt(a.enrollments) || 0;
        const enrollmentsB = parseInt(b.enrollments) || 0;
        return enrollmentsB - enrollmentsA;
      });

      // Return top N courses in the expected format
      const topCourses = validCourses.slice(0, limit).map((course: any) => {
        // Format the learner count accurately using our helper function
        const formattedStudents = formatEnrollment(course.enrollments);

        // Format the duration for display
        const formattedDuration = formatDurationInHours(course.video_content_length);

        // Format the price from price_retail column
        const formattedPrice = formatPrice(course.price_retail);

        // Format rating to show one decimal place
        const rating = course.rating_value ?
          (parseFloat(course.rating_value).toFixed(1) || '4.5') :
          '4.5';

        return {
          title: course.product_name || 'Untitled Course',
          description: course.product_subtitle || 'No description available',
          tags: [course.category_primary || 'General'],
          rating: rating,
          rating_count: course.rating_count || '',
          students: formattedStudents,
          duration: formattedDuration,
          url: course.product_url || '#',
          price: formattedPrice,
          image: course.product_image || '' // Add the course image
        };
      });

      console.log(`Returning ${topCourses.length} top courses`);
      return topCourses;
    } catch (error) {
      console.error('Failed to fetch top courses:', error);
      // Return empty array instead of fallbacks
      return [];
    }
  },

  // No fallback courses - we only use database courses
  getFallbackCourses: (_query: string) => {
    console.log('Fallback courses disabled - only using database courses');
    return [];
  },

  // Search for courses based on query (using vector-search API only)
  searchCourses: async (query: string) => {
    try {
      console.log('Searching for courses with query:', query);

      // Add "English" to the query to prioritize English courses
      const enhancedQuery = `${query} English`;
      console.log('Enhanced query with language preference:', enhancedQuery);

      // Use the vector search API exclusively to get courses from the database
      const response = await api.post('/api/vector-search', { query: enhancedQuery });
      console.log('Vector search response:', response.data);

      if (response.data && response.data.courses && response.data.courses.length > 0) {
        console.log('Raw course data from API:', response.data.courses);

        // Log the first course to examine its structure in detail
        if (response.data.courses.length > 0) {
          console.log('Detailed first course data:', JSON.stringify(response.data.courses[0], null, 2));
          console.log('Price field:', response.data.courses[0].price);
          console.log('Product price field:', response.data.courses[0].product_price);
        }

        // Map the API response to the format expected by the CourseList component
        const dbCourses = response.data.courses.map((course: any) => {
          // Format the learner count accurately using our helper function
          const formattedStudents = formatEnrollment(course.enrollments);

          // Format the duration for display
          const formattedDuration = formatDurationInHours(course.video_content_length);

          // Format the price from price_retail column
          const formattedPrice = formatPrice(course.price_retail);

          // Format rating to show one decimal place
          const rating = course.rating_value ?
            (parseFloat(course.rating_value).toFixed(1) || '4.5') :
            '4.5';

          return {
            title: course.product_name || 'Untitled Course',
            description: course.product_subtitle || 'No description available',
            tags: [course.category_primary || 'General'],
            rating: rating,
            rating_count: course.rating_count || '',
            students: formattedStudents,
            duration: formattedDuration,
            url: course.product_url || '#',
            price: formattedPrice,
            image: course.product_image || '' // Add the course image
          };
        });

        console.log('Mapped database courses:', dbCourses);
        return dbCourses;
      }

      // If no courses found, return empty array - never use fallbacks
      console.log('No courses found in database for query:', query);
      return [];
    } catch (error) {
      console.error('Course search failed:', error);
      // Return empty array instead of fallbacks
      return [];
    }
  },

  // Search for courses based on specific intent details (skill, role, technology)
  searchCoursesByIntent: async (intentDetails: {
    skill?: string | null,
    role?: string | null,
    technologies?: string | null,
    combined?: boolean,
    confidence?: {
      skill: number,
      role: number,
      technologies: number
    }
  }) => {
    try {
      console.log('Searching for courses with intent details:', intentDetails);

      // Build a search query based on the available details and confidence scores
      let searchQuery = '';
      const confidenceThreshold = 70; // Only include entities with confidence above this threshold

      // Check if we have confidence scores
      const hasConfidence = intentDetails.confidence &&
        typeof intentDetails.confidence === 'object' &&
        'skill' in intentDetails.confidence &&
        'role' in intentDetails.confidence &&
        'technologies' in intentDetails.confidence;

      // Add skill if it exists and confidence is high enough (or if no confidence data)
      if (intentDetails.skill &&
          (!hasConfidence || intentDetails.confidence!.skill >= confidenceThreshold)) {
        searchQuery += intentDetails.skill + ' ';
      }

      // Add role if it exists and confidence is high enough (or if no confidence data)
      if (intentDetails.role &&
          (!hasConfidence || intentDetails.confidence!.role >= confidenceThreshold)) {
        searchQuery += intentDetails.role + ' ';
      }

      // Add technologies if they exist and confidence is high enough (or if no confidence data)
      if (intentDetails.technologies &&
          (!hasConfidence || intentDetails.confidence!.technologies >= confidenceThreshold)) {
        searchQuery += intentDetails.technologies;
      }

      // Trim the query and ensure it's not empty
      searchQuery = searchQuery.trim();
      if (!searchQuery) {
        console.log('No search terms found in intent details or confidence too low');
        return [];
      }

      console.log('Constructed search query from intent details:', searchQuery);

      // If this is a combined query (multiple entities detected), we might want to
      // prioritize the most confident entity to get more focused results
      if (intentDetails.combined && hasConfidence) {
        console.log('Combined query detected, considering confidence scores for prioritization');

        // Find the entity with the highest confidence
        const confidences = intentDetails.confidence!;
        const highestConfidence = Math.max(
          intentDetails.skill ? confidences.skill : 0,
          intentDetails.role ? confidences.role : 0,
          intentDetails.technologies ? confidences.technologies : 0
        );

        // If skill has the highest confidence, prioritize it
        if (intentDetails.skill && confidences.skill === highestConfidence) {
          console.log('Skill has highest confidence, prioritizing in search');
          searchQuery = intentDetails.skill + ' ' + searchQuery.replace(intentDetails.skill!, '').trim();
        }
        // If role has the highest confidence, prioritize it
        else if (intentDetails.role && confidences.role === highestConfidence) {
          console.log('Role has highest confidence, prioritizing in search');
          searchQuery = intentDetails.role + ' ' + searchQuery.replace(intentDetails.role!, '').trim();
        }
        // If technologies has the highest confidence, prioritize it
        else if (intentDetails.technologies && confidences.technologies === highestConfidence) {
          console.log('Technologies has highest confidence, prioritizing in search');
          searchQuery = intentDetails.technologies + ' ' + searchQuery.replace(intentDetails.technologies!, '').trim();
        }

        console.log('Prioritized search query:', searchQuery);
      }

      // Use the existing search function with our constructed query
      return await apiService.searchCourses(searchQuery);
    } catch (error) {
      console.error('Intent-based course search failed:', error);
      return [];
    }
  },

  // Process user query for intent analysis
  processQuery: async (query: string) => {
    try {
      const response = await api.post('/api/openai-intent', { query });
      return response.data;
    } catch (error) {
      console.error('Query processing failed:', error);
      throw error;
    }
  },

  // Process chat message with history
  processChatMessage: async (query: string, chatHistory: any[], intentDetails?: any) => {
    try {
      const response = await api.post('/api/chat', {
        query,
        chatHistory,
        intent_details: intentDetails
      });
      return response.data;
    } catch (error) {
      console.error('Chat processing failed:', error);
      throw error;
    }
  },

  // Generate a description for a skill, role, or technology
  generateDescription: async (topic: string) => {
    try {
      // First try to get a description from the backend
      const response = await api.post('/api/openai-intent', {
        query: `Generate a concise description of ${topic}`
      });

      // If we have a response, extract a description
      if (response.data && response.data.intent) {
        return { description: response.data.intent };
      }

      // Fallback descriptions based on common topics
      const fallbackDescriptions: {[key: string]: string} = {
        'python': 'Python is a versatile programming language known for its readability and broad application in web development, data science, AI, and automation.',
        'javascript': 'JavaScript is the programming language of the web, enabling interactive websites and running on virtually every browser.',
        'web development': 'Web development involves creating websites and web applications using technologies like HTML, CSS, and JavaScript.',
        'data science': 'Data science combines statistics, mathematics, and programming to extract insights and knowledge from structured and unstructured data.',
        'machine learning': 'Machine learning is a subset of AI that enables systems to learn and improve from experience without explicit programming.',
        'java': 'Java is a class-based, object-oriented programming language designed for portability and cross-platform development.',
        'react': 'React is a JavaScript library for building user interfaces, particularly single-page applications with reusable components.',
        'node.js': 'Node.js is a JavaScript runtime built on Chrome\'s V8 engine, allowing developers to run JavaScript on the server side.',
        'sql': 'SQL (Structured Query Language) is used for managing and manipulating relational databases.',
        'cloud computing': 'Cloud computing provides on-demand computing resources over the internet, including storage, processing power, and applications.'
      };

      // Check if we have a fallback for this topic
      for (const key in fallbackDescriptions) {
        if (topic.toLowerCase().includes(key)) {
          return { description: fallbackDescriptions[key] };
        }
      }

      // Generic fallback
      return {
        description: `${topic} is an important skill in the technology industry, valued by employers and essential for career growth.`
      };
    } catch (error) {
      console.error('Description generation failed:', error);
      return {
        description: `${topic} is an important skill in the technology industry, valued by employers and essential for career growth.`
      };
    }
  }
};

export default apiService;
