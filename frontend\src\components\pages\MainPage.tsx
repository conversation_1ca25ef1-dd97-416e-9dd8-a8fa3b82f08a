import ChatMain from '../chat/ChatMain';
/*
import DecorativeElements from './particles/DecorativeElements';
import ParticlesBackground from './particles/ParticlesBackground';
*/
const MainPage = () => {
  return (
    <div className="min-h-screen bg-blue-900 flex justify-center items-center p-0 relative overflow-hidden">
      {/* TODO: Re-enable decorative elements after optimizing performance 
      <ParticlesBackground />
      <DecorativeElements side="left" />
      <DecorativeElements side="right" />
      */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/50 to-blue-800/50 z-0"></div>
      <ChatMain />
    </div>
  );
};

export default MainPage;
