import React, { useState, useEffect, useMemo } from 'react';
import { UserProfileDetails } from '@/components/user/types/user';
import { Button } from '@/components/ui/button';
import { ErrorMessage } from '@/components/ui/error-message';
import { SuccessMessage } from '@/components/ui/success-message';
import { EXPERIENCE_OPTIONS, USER_MESSAGES } from './utils/constants';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface UserProfileProps {
  isActive?: boolean;
  setSidebarActive: (active: boolean) => void;
  onSave: (details: Partial<UserProfileDetails>) => void;
  userProfile: UserProfileDetails;
}

const UserProfile: React.FC<UserProfileProps> = ({
  isActive = false,
  setSidebarActive,
  onSave,
  userProfile,
}) => {
  const [name, setName] = useState('');
  const [experience, setExperience] = useState('');
  const [interests, setInterests] = useState('');
  const [careerPath, setCareerPath] = useState('');
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Update state when userProfile changes
  useEffect(() => {
    setName(userProfile.name);
    setExperience(userProfile.experience);
    setInterests(userProfile.interests);
    setCareerPath(userProfile.careerPath);
  }, [userProfile]);

  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case 'name':
        setName(value);
        break;
      case 'experience':
        setExperience(value);
        break;
      case 'interests':
        setInterests(value);
        break;
      case 'careerPath':
        setCareerPath(value);
        break;
    }
    setError(null);
  };
  const handleCancel = () => {
    // Reset form fields to original values
    setName(userProfile.name);
    setExperience(userProfile.experience);
    setInterests(userProfile.interests);
    setCareerPath(userProfile.careerPath);
    // Clear any errors
    setError(null);
    // Close the sidebar
    setSidebarActive(false);
  };

  const handleSave = () => {
    setError(null);

    if (!name || !experience || !interests || !careerPath) {
      setError(USER_MESSAGES.ERROR_REQUIRED_FIELDS);
      return;
    }

    onSave({
      name,
      experience,
      interests,
      careerPath,
    });

    setFormSubmitted(true);

    setTimeout(() => {
      setSidebarActive(false);
    }, 1500);
  };

  const handleClose = () => {
    setSidebarActive(false);
    onSave({});
    setFormSubmitted(false);
    setError(null);
  };

  useEffect(() => {
    if (!isActive) {
      setFormSubmitted(false);
      setError(null);
    }
  }, [isActive]);

  const containerBaseClasses =
    'fixed right-4 top-20 w-[300px] bg-white shadow-xl border border-gray-200 rounded-lg transition-all duration-300 overflow-hidden z-40';
  const containerActiveClasses = 'opacity-100 max-h-[600px] transform-none';
  const containerInactiveClasses =
    'opacity-0 max-h-0 pointer-events-none transform translate-y-[-10px]';

  const inputBaseClasses =
    'w-full p-2 border rounded-md focus:outline-none text-sm transition-colors';
  const inputErrorClasses = 'border-red-500';
  const inputNormalClasses = 'border-gray-300 focus:border-blue-500';

  const formClasses = useMemo(
    () => ({
      container: `${containerBaseClasses} ${isActive ? containerActiveClasses : containerInactiveClasses}`,
      input: (hasError: boolean) =>
        `${inputBaseClasses} ${hasError ? inputErrorClasses : inputNormalClasses}`,
    }),
    [isActive]
  );

  return (
    <>
      {/* Dropdown arrow indicator */}
      <div
        className={`fixed right-8 top-[72px] w-0 h-0
        border-l-[8px] border-r-[8px] border-b-[8px]
        border-l-transparent border-r-transparent border-b-white
        filter drop-shadow-md z-50 transition-opacity duration-200
        ${isActive ? 'opacity-100' : 'opacity-0'}`}
      ></div>

      {/* Dropdown panel */}
      <div className={formClasses.container}>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-blue-800 text-xl font-bold">{USER_MESSAGES.HEADERS.TITLE}</h2>
            <Button
              variant="icon"
              className="text-gray-500 hover:text-gray-700"
              onClick={handleClose}
            >
              <i className="fas fa-times fa-lg"></i>
            </Button>
          </div>

          <div className="border-b border-gray-200 mb-4"></div>

          <div className="user-details-form space-y-4">
            {error && <ErrorMessage message={error} />}

            {formSubmitted && <SuccessMessage message={USER_MESSAGES.SUCCESS_SAVE} />}

            <div className="form-group">
              <label className="block text-blue-800 font-semibold mb-2 text-sm">
                {USER_MESSAGES.LABELS.NAME}
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder={USER_MESSAGES.PLACEHOLDERS.NAME}
                className={formClasses.input(error !== null && !name)}
              />
            </div>

            <div className="form-group">
              <label className="block text-blue-800 font-semibold mb-2 text-sm">
                {USER_MESSAGES.LABELS.EXPERIENCE}
              </label>
              <DropdownMenu>
                <DropdownMenuTrigger
                  className={formClasses.input(error !== null && !experience)}
                  type="button"
                >
                  <span className="text-sm">
                    {experience
                      ? EXPERIENCE_OPTIONS.find((opt) => opt.value === experience)?.label
                      : USER_MESSAGES.PLACEHOLDERS.EXPERIENCE}
                  </span>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  {EXPERIENCE_OPTIONS.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onSelect={() => handleInputChange('experience', option.value)}
                      className={experience === option.value ? 'bg-gray-100' : ''}
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="form-group">
              <label className="block text-blue-800 font-semibold mb-2 text-sm">
                {USER_MESSAGES.LABELS.INTERESTS}
              </label>
              <input
                type="text"
                value={interests}
                onChange={(e) => handleInputChange('interests', e.target.value)}
                placeholder={USER_MESSAGES.PLACEHOLDERS.INTERESTS}
                className={formClasses.input(error !== null && !interests)}
              />
            </div>

            <div className="form-group">
              <label className="block text-blue-800 font-semibold mb-2 text-sm">
                {USER_MESSAGES.LABELS.CAREER_PATH}
              </label>
              <input
                type="text"
                value={careerPath}
                onChange={(e) => handleInputChange('careerPath', e.target.value)}
                placeholder={USER_MESSAGES.PLACEHOLDERS.CAREER_PATH}
                className={formClasses.input(error !== null && !careerPath)}
              />
            </div>

            <div className="flex gap-2 mt-4">
              <Button onClick={handleSave}>{USER_MESSAGES.BUTTONS.SAVE}</Button>
              <Button onClick={() => handleCancel()}>{USER_MESSAGES.BUTTONS.CANCEL}</Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(UserProfile);
