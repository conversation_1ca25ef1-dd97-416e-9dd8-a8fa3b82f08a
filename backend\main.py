from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
import asyncpg
import os
from dotenv import load_dotenv
from fastapi.middleware.cors import CORSMiddleware
import httpx
import logging
import re
import json
from typing import Dict, Any

# ✅ Setup logging BEFORE anything else
logger = logging.getLogger("uvicorn")
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter("[%(levelname)s] %(message)s")
handler.setFormatter(formatter)
logger.addHandler(handler)

load_dotenv()

api_key = os.getenv('OPENAI_API_KEY')
if not api_key:
    logger.error("OpenAI API key not found in environment variables")
    raise ValueError("OPENAI_API_KEY must be set in .env file")
elif not api_key.startswith('sk-'):
    logger.error("Invalid OpenAI API key format")
    raise ValueError("OPENAI_API_KEY must start with 'sk-'")
else:
    logger.info("✅ OpenAI API key loaded successfully")

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Check for database connection URL (preferred method)
DATABASE_URL = os.getenv('DATABASE_URL')

# If no connection URL, get individual database configuration variables
if not DATABASE_URL:
    DB_HOST = os.getenv('DB_HOST')
    DB_PORT = os.getenv('DB_PORT')
    DB_USER = os.getenv('DB_USER')
    DB_PASSWORD = os.getenv('DB_PASSWORD')
    DB_NAME = os.getenv('DB_NAME')

    # Check if all required database environment variables are set
    if not all([DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME]):
        logger.error("Missing database configuration. Please set either DATABASE_URL or all individual DB_* variables.")
        raise ValueError("Database configuration environment variables must be set in .env file")

    # Log database configuration (without password)
    logger.info(f"Database configuration: Host={DB_HOST}, Port={DB_PORT}, User={DB_USER}, Database={DB_NAME}")
else:
    # Set these to None so they're defined for the rest of the code
    DB_HOST = DB_PORT = DB_USER = DB_PASSWORD = DB_NAME = None
    logger.info("Using database connection URL for database access")

async def connect_db():
    try:
        # Check if DATABASE_URL is provided (preferred method)
        database_url = os.getenv('DATABASE_URL')

        if database_url:
            # Connect using the connection URL
            conn = await asyncpg.connect(database_url)
            logger.info("✅ Database connection successful using connection URL")
        else:
            # Connect using individual parameters
            conn = await asyncpg.connect(
                host=DB_HOST,
                port=int(DB_PORT),
                user=DB_USER,
                password=DB_PASSWORD,
                database=DB_NAME
            )
            logger.info("✅ Database connection successful using individual parameters")

        return conn
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

class Course(BaseModel):
    product_name: str
    price_retail: str
    link_url: str
    description: str

class Query(BaseModel):
    query: str

class ChatQuery(BaseModel):
    query: str
    chatHistory: list

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def detect_intent_details(query: str) -> Dict[str, Any]:
    """
    Uses OpenAI to categorize the user's intent and extract details.
    Returns a dictionary with:
    - category: one of the intent categories
    - skill: extracted skill (if any)
    - role: extracted role (if any)
    - technologies: extracted technologies (if any)
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-3.5-turbo",
                    "messages": [
                        {
                            "role": "system",
                            "content": """Analyze the user's query and:
1. Categorize it into exactly one of these categories:
   - general_chat: For casual conversations or greetings
   - skill: When user wants to learn a particular skill
   - role_without_techstack: When user wants to become a role without mentioning tech
   - role_with_techstack: When user wants to become a role with specific tech
   - wants_to_get_into_IT_as_a_fresher: When user wants to get into IT but doesn't know where to start

2. Extract these details if present:
   - skill: The specific skill the user wants to learn
   - role: The role the user wants to become
   - technologies: Any mentioned technologies or tools

Return a JSON object with category, skill, role, and technologies. Use "None" for missing values. Example outputs:
{"category": "skill", "skill": "Python", "role": "None", "technologies": "None"}
{"category": "role_with_techstack", "skill": "None", "role": "Database Administrator", "technologies": "PostgreSQL"}"""
                        },
                        {
                            "role": "user",
                            "content": query
                        }
                    ],
                    "temperature": 0.1,
                    "response_format": {"type": "json_object"},
                    "max_tokens": 100
                },
                timeout=30.0
            )
            response.raise_for_status()
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")

            try:
                details = json.loads(content)
                # Validate required fields
                if not all(key in details for key in ["category", "skill", "role", "technologies"]):
                    raise ValueError("Missing required fields in response")

                # Validate category
                valid_categories = {
                    "general_chat",
                    "skill",
                    "role_without_techstack",
                    "role_with_techstack",
                    "wants_to_get_into_IT_as_a_fresher"
                }
                if details["category"] not in valid_categories:
                    raise ValueError(f"Invalid category: {details['category']}")

                # Clean up values
                for key in ["skill", "role", "technologies"]:
                    if details[key] == "None":
                        details[key] = None
                    elif isinstance(details[key], str):
                        details[key] = details[key].strip()

                # Adjust category based on role and technologies
                if details["role"] and not details["technologies"]:
                    details["category"] = "role_without_techstack"
                elif details["role"] and details["technologies"]:
                    details["category"] = "role_with_techstack"

                logger.info(f"Intent details: {details}")
                return details

            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"Error parsing OpenAI response: {str(e)}. Content: {content}")
                return {
                    "category": "general_chat",
                    "skill": None,
                    "role": None,
                    "technologies": None
                }

    except Exception as e:
        logger.error(f"Error detecting intent details: {str(e)}")
        return {
            "category": "general_chat",
            "skill": None,
            "role": None,
            "technologies": None
        }

@app.get("/")
async def read_root():
    return {"message": "✅ Backend is running... 🚀"}

@app.get("/courses")
async def get_courses():
    conn = await connect_db()
    courses = await conn.fetch("SELECT * FROM udemy_course_catalog")
    await conn.close()
    return courses

@app.post("/courses")
async def create_course(course: Course):
    conn = await connect_db()
    await conn.execute(
        "INSERT INTO udemy_course_catalog (product_name, price_retail, product_url, description_short) VALUES ($1, $2, $3, $4)",
        course.product_name, course.price_retail, course.link_url, course.description
    )
    await conn.close()
    return {"message": "Course created successfully"}

@app.post("/api/openai-intent")
async def openai_intent(query: Query):
    logger.info("openai_intent function called")
    if not query.query:
        raise HTTPException(status_code=400, detail="Query is required")

    try:
        async with httpx.AsyncClient() as client:
            logger.info(f"Sending request to OpenAI API for query: {query.query}")
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": f"Determine the intent of: {query.query}"}],
                    "max_tokens": 100,
                    "temperature": 0.5
                },
                timeout=60.0
            )
            response.raise_for_status()
            data = response.json()

            intent = data.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            logger.info(f"Detected intent: {intent}")

            # Get the intent details including category
            intent_details = await detect_intent_details(query.query)

            return {
                "intent": intent,
                "category": intent_details["category"],
                "details": {
                    "skill": intent_details["skill"],
                    "role": intent_details["role"],
                    "technologies": intent_details["technologies"]
                }
            }

    except httpx.HTTPError as e:
        logger.error(f"OpenAI API error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"OpenAI API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

@app.post("/api/vector-search")
async def vector_search(query: Query):
    if not query.query:
        raise HTTPException(status_code=400, detail="Query is required")

    conn = await connect_db()
    try:
        db_result = await conn.fetchrow(
            "SELECT vector_embedding FROM udemy_course_catalog WHERE product_name ILIKE $1 LIMIT 1",
            f"%{query.query}%"
        )

        if db_result:
            query_embedding = db_result["vector_embedding"]
            logging.info("✅ Using existing embedding from PostgreSQL.")
        else:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.openai.com/v1/embeddings",
                    headers={"Authorization": f"Bearer {api_key}"},
                    json={"input": query.query, "model": "text-embedding-ada-002"},
                    timeout=60.0
                )
            query_embedding = response.json().get("data", [{}])[0].get("embedding")

            if not query_embedding:
                raise HTTPException(status_code=500, detail="Embedding generation failed")

            # Skip storing the embedding to avoid primary key conflicts
            logging.info("✅ Using generated embedding without storing it")

        # Convert the embedding list to a string format that PostgreSQL can understand
        if isinstance(query_embedding, list):
            # Format the embedding as a string that PostgreSQL can parse as a vector
            embedding_str = f"[{','.join(str(x) for x in query_embedding)}]"
            logging.info("✅ Converted embedding list to string format for PostgreSQL")
        else:
            # If it's already a string, use it as is
            embedding_str = query_embedding

        result = await conn.fetch(
            """
            SELECT *
            FROM (
                SELECT
                    product_name,
                    product_subtitle,
                    price_retail,
                    price_currency,
                    product_url,
                    rating_value,
                    rating_count,
                    enrollments,
                    video_content_length,
                    product_image
                FROM udemy_course_catalog
                WHERE
                    product_url IS NOT NULL
                    AND price_retail IS NOT NULL
                    AND course_language = 'en'
                    AND is_it_course = true
                    AND video_content_length IS NOT NULL
                    AND video_content_length != '0m'
                    AND enrollments IS NOT NULL
                    AND enrollments > 0
                ORDER BY vector_embedding <=> $1::vector
                LIMIT 50
            ) AS top_matches
            ORDER BY enrollments DESC
            LIMIT 3
            """,
            embedding_str
        )
        return {"courses": [dict(row) for row in result]}
    finally:
        await conn.close()

@app.post("/api/chat")
async def chat(query: ChatQuery):
    if not query.query:
        raise HTTPException(status_code=400, detail="Query is required")

    intent_response = await openai_intent(Query(query=query.query))
    intent = intent_response["intent"].lower()
    category = intent_response["category"]
    details = intent_response["details"]

    logger.info(f"[CHAT] Extracted intent from user input: {intent}")
    logger.info(f"[CHAT] Detected category: {category} {details}")

    # We don't need to get course recommendations here
    # The frontend will handle this separately

    async with httpx.AsyncClient() as client:
        response = await client.post(
            "https://api.openai.com/v1/chat/completions",
            headers={"Authorization": f"Bearer {api_key}"},
            json={
                "model": "gpt-3.5-turbo",
                "messages": query.chatHistory + [{"role": "user", "content": query.query}],
                "max_tokens": 2000,
                "temperature": 0.7
            },
            timeout=60.0
        )
    chat_response = response.json().get("choices", [{}])[0].get("message", {}).get("content", "").strip()

    job_titles = re.findall(r"👨‍💼\s*([^\n]+)", chat_response)
    buttons_html = ""
    if job_titles:
        buttons_html += "<div style='margin-top: 10px;'>"
        for title in job_titles:
            buttons_html += f"<button style='background-color: blue; color: white; margin: 5px; padding: 10px; border: none; border-radius: 5px;'>{title}</button> "
        buttons_html += "</div>"

    chat_response += buttons_html

    return {"response": f"{chat_response}"}

@app.get("/health")
async def health_check():
    status = {
        "database": False,
        "openai": False,
        "env_vars": {
            "db_host": bool(os.getenv('DB_HOST')),
            "db_user": bool(os.getenv('DB_USER')),
            "db_name": bool(os.getenv('DB_NAME')),
            "openai_key": bool(os.getenv('OPENAI_API_KEY')),
        }
    }

    try:
        conn = await connect_db()
        await conn.close()
        status["database"] = True
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://api.openai.com/v1/models",
                headers={"Authorization": f"Bearer {api_key}"}
            )
            status["openai"] = response.status_code == 200
    except Exception as e:
        logger.error(f"OpenAI health check failed: {str(e)}")

    return status