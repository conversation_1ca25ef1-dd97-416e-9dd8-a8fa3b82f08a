import React from 'react';

interface DecorativeElementsProps {
  side: 'left' | 'right';
}

const DecorativeElements = ({ side }: DecorativeElementsProps) => {
  const isLeft = side === 'left';
  const transformClasses = isLeft
    ? ['-rotate-45', 'rotate-30', 'rotate-15']
    : ['rotate-45', '-rotate-30', '-rotate-15'];
  const positionClasses = isLeft
    ? ['left-[30%]', 'left-[20%]', 'left-[40%]']
    : ['right-[30%]', 'right-[20%]', 'right-[40%]'];

  return (
    <div
      className={`absolute ${isLeft ? 'left-0' : 'right-0'} top-0 h-full w-[15%] flex flex-col justify-center items-center`}
    >
      <div className="w-24 h-24 rounded-full bg-white opacity-10 mb-8"></div>
      <div className="w-16 h-16 rounded-full bg-white opacity-10 mb-8"></div>
      <div className="w-20 h-20 rounded-full bg-white opacity-10"></div>

      {/* Decorative lines */}
      <div
        className={`absolute top-[20%] ${positionClasses[0]} w-[150px] h-[1px] bg-white opacity-20 transform ${transformClasses[0]}`}
      ></div>
      <div
        className={`absolute top-[40%] ${positionClasses[1]} w-[100px] h-[1px] bg-white opacity-20 transform ${transformClasses[1]}`}
      ></div>
      <div
        className={`absolute top-[70%] ${positionClasses[2]} w-[120px] h-[1px] bg-white opacity-20 transform ${transformClasses[2]}`}
      ></div>
    </div>
  );
};

export default DecorativeElements;
