# React and TypeScript Rules
*.tsx:
  - Use functional components with TypeScript
  - Use React hooks for state management
  - Follow React best practices and patterns
  - Use proper TypeScript types and interfaces
  - Avoid any type when possible
  - Use absolute imports (e.g., @/components) instead of relative imports when possible

# shadcn-ui Rules
components/ui/*:
  - The project is using Shadcn UI for components.
  - Follow shadcn-ui component patterns
  - Use Radix UI primitives when needed
  - Maintain consistent styling with shadcn-ui
  - Keep components modular and reusable
  - Every single time we need to run a shadcn command

# Tailwind CSS Rules
*.{tsx,css}:
  - The project is using Tailwind CSS for styling.
  - Use Tailwind utility classes
  - Follow mobile-first responsive design
  - Use consistent color scheme from tailwind.config
  - Prefer Tailwind classes over custom CSS
  - Use @apply only when necessary

# General Rules
*:
  - Use ESLint and Prettier for code formatting
  - Follow component-based architecture
  - Maintain consistent file structure
  - Use proper error handling
  - Write meaningful comments 