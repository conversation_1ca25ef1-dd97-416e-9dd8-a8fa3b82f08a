import * as React from 'react';
import { cn } from '@/lib/utils';
import { AlertCircle } from 'lucide-react';

interface ErrorMessageProps extends React.HTMLAttributes<HTMLDivElement> {
  message: string;
}

const ErrorMessage = React.forwardRef<HTMLDivElement, ErrorMessageProps>(
  ({ className, message, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded animate-pulse transition-all duration-300 ease-in-out',
          className
        )}
        {...props}
      >
        <p className="text-sm font-medium flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          {message}
        </p>
      </div>
    );
  }
);
ErrorMessage.displayName = 'ErrorMessage';

export { ErrorMessage };
