import { useState, useEffect, useCallback } from 'react';
import {
  ERROR_MESSAGES,
  CONNECTION_STATUS,
  ConnectionStatus,
} from '@/components/chat/utils/constants';
import axiosInstance from '@/axios/axiosInstance';

interface HealthStatus {
  status: ConnectionStatus;
  message: string;
}

export const useHealthCheck = () => {
  const [healthStatus, setHealthStatus] = useState<HealthStatus>({
    status: CONNECTION_STATUS.CHECKING,
    message: ERROR_MESSAGES.CHECKING_CONNECTION,
  });

  const checkHealth = useCallback(async (): Promise<boolean> => {
    try {
      setHealthStatus({
        status: CONNECTION_STATUS.CHECKING,
        message: ERROR_MESSAGES.CHECKING_CONNECTION,
      });

      const response = await axiosInstance.get('/health');
      if (!response || !response.data) {
        return handleHealthCheckError(new Error('Invalid response from health check'));
      }
      const { database, openai } = response.data;
      const isHealthy = database && openai;

      setHealthStatus({
        status: isHealthy ? CONNECTION_STATUS.CONNECTED : CONNECTION_STATUS.DISCONNECTED,
        message: isHealthy ? ERROR_MESSAGES.CONNECTION_SUCCESS : ERROR_MESSAGES.BACKEND_ERROR,
      });

      return isHealthy;
    } catch (error) {
      return handleHealthCheckError(error);
    }
  }, []);

  useEffect(() => {
    checkHealth();
  }, [checkHealth]);

  const handleHealthCheckError = (error: unknown) => {
    console.error('Health check failed:', error);
    setHealthStatus({
      status: CONNECTION_STATUS.DISCONNECTED,
      message: ERROR_MESSAGES.BACKEND_ERROR,
    });
    return false;
  };
  return {
    healthStatus,
    checkHealth,
  };
};
