import React, { useEffect, useState } from 'react';

interface Course {
  title: string;
  description: string;
  tags: string[];
  rating: string;
  rating_count?: string;
  students: string;
  duration: string;
  image?: string;
  url?: string;
  price?: string;
}

interface ChatSectionCardsProps {
  courses: Course[];
}

const ChatSectionCards: React.FC<ChatSectionCardsProps> = ({ courses }) => {
  const [visibleCards, setVisibleCards] = useState<boolean[]>([]);

  useEffect(() => {
    // Animate cards appearing one by one
    const newVisibleCards = courses.map(() => false);
    courses.forEach((_, index) => {
      setTimeout(() => {
        setVisibleCards((prev) => {
          const updated = [...prev];
          updated[index] = true;
          return updated;
        });
      }, 100 * index);
    });
    setVisibleCards(newVisibleCards);
  }, [courses]);

  return (
    <div className="course-cards-section w-2/5 bg-white/95 rounded-xl p-5 opacity-100 transition-all duration-500">
      <h2 className="text-blue-800 text-xl font-bold mb-4 pb-2 border-b-2 border-blue-800">
        Course Recommendations
      </h2>

      {courses.map((course, index) => (
        <div
          key={index}
          className={`course-card bg-white rounded-xl p-4 mb-4 shadow-md hover:translate-x-1 transition-all duration-300 ${
            visibleCards[index] ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-5'
          }`}
        >
          {/* Course image - full width as in the provided image */}
          <div className="mb-4 -mx-4 -mt-4">
            <div className="relative w-full">
              {/* Full width course image with blue background */}
              <div className="bg-blue-900 w-full h-[100px] rounded-t-xl"></div>

              {/* Company logo overlay */}
              <div className="absolute top-2 left-2 z-20 bg-white rounded-md px-2 py-1">
                <span className="text-xs font-bold text-blue-900">iversity</span>
              </div>

              {/* Profile image overlay */}
              <div className="absolute top-[30px] left-1/2 transform -translate-x-1/2">
                <img
                  src={course.image || 'https://via.placeholder.com/80'}
                  alt="Instructor"
                  className="w-[80px] h-[80px] object-cover rounded-full border-4 border-white"
                  onError={(e) => {
                    // Replace with placeholder if image fails to load
                    (e.target as HTMLImageElement).src = 'https://via.placeholder.com/80';
                  }}
                />
              </div>
            </div>

            {/* Course title - positioned below the image with enough space for the profile image */}
            <div className="mt-16 text-center px-4">
              <h3 className="text-blue-800 text-lg font-semibold">{course.title}</h3>
            </div>
          </div>

          {/* Course description */}
          <p className="text-gray-600 mb-3 text-sm">{course.description}</p>

          {/* Course details */}
          <div className="course-details flex flex-wrap text-xs text-gray-500 mb-2">
            <div className="mr-4">
              Level: <span className="font-medium">Beginner</span>
            </div>
            <div className="mr-4">
              Duration: <span className="font-medium">{course.duration}</span>
            </div>
            <div>
              Learners: <span className="font-medium">{course.students}</span>
            </div>
          </div>

          {/* Course rating */}
          <div className="rating flex items-center text-sm">
            <div className="stars text-yellow-400 mr-2">
              {'★'.repeat(Math.floor(parseFloat(course.rating)))}
            </div>
            <div className="rating-value text-gray-600">
              {course.rating} {course.rating_count && `(${course.rating_count})`}
            </div>

            {/* Price tag */}
            {course.price && (
              <div className="ml-auto">
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                  {course.price}
                </span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ChatSectionCards;
