import { FC } from 'react';
import LoadingDots from '@/components/chat/LoadingDots';
import BotMessage from '@/components/chat/BotMessage';
import { Message } from '@/components/chat/types/chat';
import { MESSAGE_TYPES, MESSAGE_CLASSES } from '@/components/chat/utils/constants';

interface ChatMessagesProps {
  messages: Message[];
}

const getMessageClassName = (type: Message['type']): string => {
  switch (type) {
    case MESSAGE_TYPES.USER:
      return `${MESSAGE_CLASSES.BASE} ${MESSAGE_CLASSES.USER}`;
    case MESSAGE_TYPES.LOADING:
      return `${MESSAGE_CLASSES.BASE} ${MESSAGE_CLASSES.LOADING}`;
    case MESSAGE_TYPES.BOT:
      return `${MESSAGE_CLASSES.BASE} ${MESSAGE_CLASSES.BOT}`;
    default:
      return MESSAGE_CLASSES.BASE;
  }
};

const ChatMessages: FC<ChatMessagesProps> = ({ messages }) => {
  return (
    <div className="flex flex-col space-y-2 sm:space-y-3 md:space-y-4 p-2 sm:p-3 md:p-4 overflow-y-auto">
      {messages.map((message, index) => renderMessage(index, message))}
    </div>
  );

  function renderMessage(index: number, message: Message) {
    return (
      <div key={index} className={getMessageClassName(message.type)}>
        {message.type === MESSAGE_TYPES.LOADING && <LoadingDots />}
        {message.type === MESSAGE_TYPES.BOT && <BotMessage text={message.text} />}
        {message.type === MESSAGE_TYPES.USER && message.text}
      </div>
    );
  }
};

export default ChatMessages;
