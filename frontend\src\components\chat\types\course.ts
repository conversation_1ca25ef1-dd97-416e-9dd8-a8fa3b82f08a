export interface Course {
  title: string;
  description: string;
  tags: string[];
  rating: string;
  rating_count?: string;
  students: string;
  duration: string;
  url: string;
  price: string;
  image?: string;
}

// Course from DB
export interface CourseFromDB {
  course_language: string | null;
  product_name: string | null;
  product_subtitle: string | null;
  category_primary: string | null;
  rating_value: string | null;
  rating_count: string | null;
  enrollments: number | null;
  video_content_length: number | null;
  price_retail: number | null;
  product_url: string | null;
  product_image: string | null;
}
