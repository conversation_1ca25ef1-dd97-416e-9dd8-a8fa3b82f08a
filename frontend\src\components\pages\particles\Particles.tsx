interface ParticlesProps {
  count?: number;
}

const Particles = ({ count = 30 }: ParticlesProps) => {
  // Generate particles array
  const particles = Array.from({ length: count }, (_, i) => ({
    id: i,
    left: `${Math.random() * 100}%`,
    delay: `${Math.random() * 10}s`,
    size: 5 + Math.random() * 3,
  }));

  return (
    <div className="particles" id="particles">
      {/* Pre-rendered particles with larger sizes - reduced number */}
      <div
        className="particle"
        style={{
          left: '10%',
          animationDelay: '0s',
          width: '7px',
          height: '7px',
        }}
      ></div>
      <div
        className="particle"
        style={{
          left: '30%',
          animationDelay: '2s',
          width: '8px',
          height: '8px',
        }}
      ></div>
      <div
        className="particle"
        style={{
          left: '50%',
          animationDelay: '4s',
          width: '7px',
          height: '7px',
        }}
      ></div>
      <div
        className="particle"
        style={{
          left: '70%',
          animationDelay: '6s',
          width: '8px',
          height: '8px',
        }}
      ></div>
      <div
        className="particle"
        style={{
          left: '90%',
          animationDelay: '8s',
          width: '7px',
          height: '7px',
        }}
      ></div>
      <div
        className="particle"
        style={{
          left: '20%',
          animationDelay: '1s',
          width: '8px',
          height: '8px',
        }}
      ></div>
      <div
        className="particle"
        style={{
          left: '60%',
          animationDelay: '5s',
          width: '7px',
          height: '7px',
        }}
      ></div>
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="particle"
          style={{
            left: particle.left,
            animationDelay: particle.delay,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
          }}
        />
      ))}
    </div>
  );
};

export default Particles;
