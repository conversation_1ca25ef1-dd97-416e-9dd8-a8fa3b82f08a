import React from 'react';
import { Course } from '@/components/chat/types/course';
import { DEFAULT_VIEW_MESSAGES } from './utils/constants';

interface CourseDefaultViewProps {
  courses?: Course[];
}

const CourseDefaultView: React.FC<CourseDefaultViewProps> = () => {
  return (
    <div className="flex-grow flex flex-col items-center justify-center text-center text-gray-500 px-2">
      <div className="mb-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#d1d5db"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
        </svg>
      </div>
      <p className="font-medium text-blue-800 mb-2">{DEFAULT_VIEW_MESSAGES.NO_RECOMMENDATIONS}</p>
      <p className="mb-2 text-sm">{DEFAULT_VIEW_MESSAGES.INSTRUCTION}</p>
      <p className="text-xs italic mb-4">{DEFAULT_VIEW_MESSAGES.EXAMPLES}</p>
      <p className="text-xs text-gray-400">{DEFAULT_VIEW_MESSAGES.LIMIT_NOTE}</p>
    </div>
  );
};

export default CourseDefaultView;
