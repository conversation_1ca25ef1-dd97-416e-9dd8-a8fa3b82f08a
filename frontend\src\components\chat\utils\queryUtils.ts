/* eslint-disable no-console */
import { LEARNING_KEYWORDS, CHAT_STATIC_MESSAGES } from '@/components/chat/utils/constants';
import { ChatRole, MessageType } from '@/components/chat/types/chat';

/**
 * Default messages used to initialize the chat UI
 */
export const defaultMessages = [
  {
    text: CHAT_STATIC_MESSAGES.ASSISTANT_DEFAULT,
    type: 'bot' as MessageType,
  },
];

/**
 * Default chat history used to initialize the chat
 */
export const defaultChatHistory = [
  {
    role: 'system' as ChatRole,
    content: CHAT_STATIC_MESSAGES.SYSTEM_DEFAULT,
  },
  {
    role: 'assistant' as ChatR<PERSON>,
    content: CHAT_STATIC_MESSAGES.ASSISTANT_DEFAULT,
  },
];

/**
 * Checks if a user message indicates a learning-related query
 * @param userMessage - The user's input message
 * @returns boolean indicating if the message is learning-related
 */
export const isLearningQuery = (userMessage: string): boolean => {
  const lowerCaseMessage = userMessage.toLowerCase();
  return (
    lowerCaseMessage.includes(LEARNING_KEYWORDS.COURSE) ||
    lowerCaseMessage.includes(LEARNING_KEYWORDS.LEARN) ||
    lowerCaseMessage.includes(LEARNING_KEYWORDS.STUDY) ||
    lowerCaseMessage.includes(LEARNING_KEYWORDS.RECOMMEND) ||
    lowerCaseMessage.includes(LEARNING_KEYWORDS.TRAINING)
  );
};

/**
 * Fallback descriptions for common technology topics
 * Used when the backend API fails to generate a description
 */
export const fallbackDescriptions: { [key: string]: string } = {
  python:
    'Python is a versatile programming language known for its readability and broad application in web development, data science, AI, and automation.',
  javascript:
    'JavaScript is the programming language of the web, enabling interactive websites and running on virtually every browser.',
  'web development':
    'Web development involves creating websites and web applications using technologies like HTML, CSS, and JavaScript.',
  'data science':
    'Data science combines statistics, mathematics, and programming to extract insights and knowledge from structured and unstructured data.',
  'machine learning':
    'Machine learning is a subset of AI that enables systems to learn and improve from experience without explicit programming.',
  java: 'Java is a class-based, object-oriented programming language designed for portability and cross-platform development.',
  react:
    'React is a JavaScript library for building user interfaces, particularly single-page applications with reusable components.',
  'node.js':
    "Node.js is a JavaScript runtime built on Chrome's V8 engine, allowing developers to run JavaScript on the server side.",
  sql: 'SQL (Structured Query Language) is used for managing and manipulating relational databases.',
  'cloud computing':
    'Cloud computing provides on-demand computing resources over the internet, including storage, processing power, and applications.',
};

export interface IntentDetails {
  skill?: string | null;
  role?: string | null;
  technologies?: string | null;
}

/**
 * Builds a search query string from intent details
 * @param intentDetails - Object containing skill, role, and technologies information
 * @returns A formatted search query string or null if no valid search terms are found
 */
export const buildSearchQueryFromIntent = (intentDetails: IntentDetails): string | null => {
  let searchQuery = '';

  if (intentDetails.skill) {
    searchQuery += intentDetails.skill + ' ';
  }

  if (intentDetails.role) {
    searchQuery += intentDetails.role + ' ';
  }

  if (intentDetails.technologies) {
    searchQuery += intentDetails.technologies;
  }

  searchQuery = searchQuery.trim();
  if (!searchQuery) {
    console.log('No search terms found in intent details');
    return null;
  }

  console.log('Constructed search query from intent details:', searchQuery);
  return searchQuery;
};
