import { useRef, useEffect } from 'react';
import ChatMessages from '@/components/chat/ChatMessages';
import { Message } from '@/components/chat/types/chat';
import { CHAT_MESSAGES } from '@/components/chat/utils/constants';

interface ChatAreaProps {
  messages: Message[];
  isBackendConnected: boolean;
}

const ChatArea = ({ messages, isBackendConnected }: ChatAreaProps) => {
  // Reference to the chat messages container for auto-scrolling
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the latest message when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      // Scroll to the bottom of the chat container with smooth animation
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, [messages]); // Re-run when messages change

  return (
    <div
      ref={chatContainerRef}
      className="flex-grow px-6 py-6 overflow-y-auto scroll-smooth relative"
    >
      {messages.length > 0 ? (
        <ChatMessages messages={messages} />
      ) : (
        <div className="h-full flex flex-col items-center justify-center text-gray-600">
          {isBackendConnected ? (
            <>
              <p className="mb-2 text-center">{CHAT_MESSAGES.EMPTY_STATE.CONNECTED}</p>
            </>
          ) : (
            <>
              <p className="mb-2 text-center">{CHAT_MESSAGES.EMPTY_STATE.CONNECTING}</p>
              <p className="text-center">{CHAT_MESSAGES.EMPTY_STATE.CHECK_SERVER}</p>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default ChatArea;
