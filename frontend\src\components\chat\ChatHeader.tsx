/* eslint-disable no-console */
import { FC, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { CONNECTION_STATUS, HEADER_MESSAGES, LOG_MESSAGES } from './utils/constants';
import UserProfile from '@/components/user/UserProfile';
import { UserProfileDetails } from '@/components/user/types/user';
import { useHealthCheck } from '@/components/chat/hooks/useHealthCheck';

interface ChatHeaderProps {
  handleReset: () => void;
  userProfile: UserProfileDetails;
  setIsBackendConnected: (status: boolean) => void;
  onSaveUserProfile: (details: Partial<UserProfileDetails>) => void;
}

const ChatHeader: FC<ChatHeaderProps> = ({
  handleReset,
  userProfile,
  setIsBackendConnected,
  onSaveUserProfile,
}: ChatHeaderProps) => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const { healthStatus, checkHealth } = useHealthCheck();

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  // Update parent component's connection status
  useEffect(() => {
    setIsBackendConnected(healthStatus.status === CONNECTION_STATUS.CONNECTED);
  }, [healthStatus, setIsBackendConnected]);

  const resetButtonClickHandler = () => {
    console.log(LOG_MESSAGES.RESET);
    handleReset();
    setSidebarActive(false);
  };

  return (
    <>
      <div className="py-2 px-4 border-b border-gray-200 flex flex-col items-center">
        {/* Title and subtitle centered */}
        <div className="text-center w-full mb-1">
          <h1 className="text-2xl font-bold text-blue-800">{HEADER_MESSAGES.TITLE}</h1>
          <p className="text-gray-600 text-sm">{HEADER_MESSAGES.SUBTITLE}</p>
        </div>

        {/* Backend status centered */}
        <div className="flex justify-center items-center w-full mb-2">
          <p
            className={`${healthStatus.status === 'connected' ? 'text-green-500' : 'text-red-500'} text-xs`}
          >
            <i
              className={`fas fa-circle text-xs mr-1 ${healthStatus.status === 'connected' ? 'text-green-500' : 'text-red-500'}`}
            ></i>
            {healthStatus.message}
          </p>
          {healthStatus.status === CONNECTION_STATUS.DISCONNECTED && (
            <button
              onClick={checkHealth}
              className="text-xs text-blue-500 hover:text-blue-700 underline ml-2"
            >
              {HEADER_MESSAGES.STATUS.RETRY}
            </button>
          )}
        </div>

        {/* Action buttons row - responsive layout */}
        <div className="flex flex-col sm:flex-row justify-center sm:justify-end items-center w-full space-y-2 sm:space-y-0">
          {/* Action buttons with responsive spacing and sizing */}
          <div className="md:gap-4 w-full sm:w-auto px-2 sm:px-4 md:px-6">
            <Button onClick={resetButtonClickHandler} variant="secondary" className="mr-2">
              {HEADER_MESSAGES.BUTTONS.RESET}
            </Button>
            <Button onClick={toggleSidebar}>{HEADER_MESSAGES.BUTTONS.USER_DETAILS}</Button>
          </div>
        </div>
      </div>
      {/* User profile sidebar */}
      <UserProfile
        isActive={sidebarActive}
        setSidebarActive={setSidebarActive}
        userProfile={userProfile}
        onSave={onSaveUserProfile}
      />
    </>
  );
};

export default ChatHeader;
