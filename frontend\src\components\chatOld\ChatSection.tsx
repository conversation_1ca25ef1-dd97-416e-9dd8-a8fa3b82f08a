import React, { useState, useRef, useEffect } from 'react';
import ChatSectionCards from '@/components/chatOld/ChatSectionCards';
import { Course } from '@/components/chat/types/course';
import { Button } from '../ui/button';

interface ChatSectionProps {
  userProfile: {
    name: string;
    experience: string;
    interests: string;
    careerPath: string;
  };
  onShowCourses: (courses: Course[]) => void;
  courseCardsVisible: boolean;
  recommendedCourses: Course[];
}

interface Message {
  text: string;
  type: 'user' | 'bot';
}

const ChatSection: React.FC<ChatSectionProps> = ({
  userProfile,
  onShowCourses,
  courseCardsVisible,
  recommendedCourses,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of messages when new messages are added
  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Add welcome message when user profile is set
  useEffect(() => {
    if (userProfile.name) {
      setMessages([
        {
          text: `Hello ${userProfile.name}! I see you have ${userProfile.experience} of experience and are interested in ${userProfile.interests}. Let's discuss your career path in ${userProfile.careerPath}. How can I help you today?`,
          type: 'bot',
        },
      ]);
    }
  }, [userProfile]);

  const sendMessage = () => {
    if (input.trim() === '') return;

    // Add user message
    const userMessage: Message = {
      text: input,
      type: 'user',
    };
    setMessages((prev) => [...prev, userMessage]);

    // Clear input
    setInput('');

    // Simulate bot response after a delay
    setTimeout(() => {
      const botMessage: Message = {
        text: 'I understand your query. Let me help you with that.',
        type: 'bot',
      };
      setMessages((prev) => [...prev, botMessage]);

      // Show course recommendations (for demo purposes)
      if (input.toLowerCase().includes('course') || input.toLowerCase().includes('recommend')) {
        const sampleCourses: Course[] = [
          {
            title: 'Web Development Fundamentals',
            description: 'Learn the basics of web development with HTML, CSS, and JavaScript.',
            tags: ['HTML', 'CSS', 'JavaScript'],
            rating: '4.8',
            students: '1.2k',
            duration: '8 weeks',
            url: '#',
            price: 'Paid',
            image: 'https://via.placeholder.com/80',
          },
          {
            title: 'Python Programming',
            description: 'Master Python programming from basics to advanced concepts.',
            tags: ['Python', 'Programming', 'Backend'],
            rating: '4.9',
            students: '2.5k',
            duration: '10 weeks',
            url: '#',
            price: 'Paid',
            image: 'https://via.placeholder.com/80',
          },
          {
            title: 'Data Science Essentials',
            description: 'Introduction to data science and machine learning.',
            tags: ['Data Science', 'ML', 'Analytics'],
            rating: '4.7',
            students: '1.8k',
            duration: '12 weeks',
            url: '#',
            price: 'Paid',
            image: 'https://via.placeholder.com/80',
          },
        ];
        onShowCourses(sampleCourses);
      }
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  return (
    <>
      <div
        className={`chat-section transition-all duration-500 ${courseCardsVisible ? 'w-3/5' : 'w-full'}`}
      >
        <div className="header text-center mb-6">
          <h1 className="text-blue-800 text-4xl font-bold mb-3 animate-slideDown">
            Mentoring Agent
          </h1>
          <p className="text-gray-600">Your AI-powered guide to career success</p>
        </div>

        <div className="chat-container border-2 border-gray-300 rounded-xl overflow-hidden flex flex-col h-[550px] w-full">
          <div ref={messagesContainerRef} className="chat-messages flex-grow p-5 overflow-y-auto">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`message p-4 rounded-xl max-w-[75%] my-4 opacity-0 animate-messageAppear ${
                  message.type === 'bot' ? 'bg-blue-100 mr-auto' : 'bg-blue-800 text-white ml-auto'
                }`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {message.text}
              </div>
            ))}
          </div>

          <div className="input-area p-5 bg-gray-100 flex gap-4">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message here..."
              className="flex-grow p-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none"
            />
            <Button variant="default" onClick={sendMessage}>
              Send
            </Button>
          </div>
        </div>
      </div>

      {courseCardsVisible && <ChatSectionCards courses={recommendedCourses} />}
    </>
  );
};

export default ChatSection;
