@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Playfair+Display:wght@400;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layout and background */
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  background: #1b1464;
  color: #1a1a1a;
  position: relative;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
}

.main-heading {
  font-family: 'Playfair Display', serif;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  line-height: 1.2;
  margin-bottom: 0.75rem;
  font-weight: 800;
  font-size: 1rem; /* Significantly decreased from 1.25rem */
  white-space: nowrap;
}

body::before {
  display: none;
}

.wave-vector {
  position: fixed;
  width: 200%;
  height: 4px;
  left: -50%;
  transform-origin: 50% 50%;
  border-radius: 2px;
  opacity: 0;
  animation: appear-disappear 15s infinite;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.35),
    rgba(255, 255, 255, 0.5),
    rgba(255, 255, 255, 0.35)
  );
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
  mask-image: linear-gradient(to right, transparent, black, transparent);
  -webkit-mask-image: linear-gradient(to right, transparent, black, transparent);
}

.wave-vector:nth-child(1) {
  top: 10%;
  animation-delay: 0s;
  transform: rotate(0deg);
}
.wave-vector:nth-child(2) {
  top: 20%;
  animation-delay: 0.5s;
  transform: rotate(40deg);
}
.wave-vector:nth-child(3) {
  top: 30%;
  animation-delay: 1s;
  transform: rotate(80deg);
}
.wave-vector:nth-child(4) {
  top: 40%;
  animation-delay: 1.5s;
  transform: rotate(120deg);
}
.wave-vector:nth-child(5) {
  top: 50%;
  animation-delay: 2s;
  transform: rotate(160deg);
}
.wave-vector:nth-child(6) {
  top: 60%;
  animation-delay: 2.5s;
  transform: rotate(200deg);
}
.wave-vector:nth-child(7) {
  top: 70%;
  animation-delay: 3s;
  transform: rotate(240deg);
}
.wave-vector:nth-child(8) {
  top: 80%;
  animation-delay: 3.5s;
  transform: rotate(280deg);
}
.wave-vector:nth-child(9) {
  top: 90%;
  animation-delay: 4s;
  transform: rotate(320deg);
}

@keyframes appear-disappear {
  0% {
    opacity: 0;
    transform: translateY(0) rotate(var(--rotation, 0deg));
  }
  20%,
  50%,
  80% {
    opacity: 0.5;
  }
  50% {
    transform: translateY(10px) rotate(calc(var(--rotation, 0deg) + 180deg));
  }
  100% {
    opacity: 0;
    transform: translateY(0) rotate(calc(var(--rotation, 0deg) + 360deg));
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 1s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: slideUp 1s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 48px;
  background: rgba(46, 49, 146, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  z-index: 10;
  animation: slideDown 1s ease-out forwards;
}

.content-wrapper {
  position: relative;
  height: calc(100vh - 48px);
  overflow: hidden; /* Changed from overflow-y: auto */
  padding: 2rem 1rem;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 7rem; /* Increased from 5rem to move content down */
  -webkit-overflow-scrolling: touch;
}

.form-container {
  width: 100%;
  max-width: 1000px; /* Increased from 900px for consistency */
  margin: 0.15rem auto;
  padding: 0.75rem 1rem;
  background: white;
}

.form-container .flex.justify-between {
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding-top: 0.5rem;
  margin-top: auto;
}

.form-container .space-y-6 {
  margin-bottom: 0.75rem;
}

.chat-interface-container {
  width: 100%;
  max-width: 1280px; /* Explicitly set larger width */
  margin: 0 auto;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  margin-top: 2rem;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  height: auto;
  min-height: 300px; /* Decreased from 400px */
  max-height: calc(100vh - 400px); /* Decreased from 300px */
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
  -webkit-overflow-scrolling: touch;
}

/* Chat Messages Styling */
.message {
  max-width: 80%;
  margin: 0.5rem 0;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  line-height: 1.4;
  animation: fadeIn 0.3s ease-in-out;
  word-wrap: break-word;
}

/* User Message */
.message.user {
  margin-left: auto;
  background-color: #2e3192;
  color: white;
  border-top-right-radius: 0.25rem;
  box-shadow: 0 2px 4px rgba(46, 49, 146, 0.2);
}

/* Bot Message */
.message.bot {
  margin-right: auto;
  background-color: #f0f2f5;
  color: #1a1a1a;
  border-top-left-radius: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Message animations */
.fade-in {
  opacity: 0;
  animation: messageAppear 0.3s ease-out forwards;
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Message container spacing */
.message-wrapper {
  margin: 0.75rem 0;
  display: flex;
  flex-direction: column;
}

/* Style markdown content inside messages */
.message .prose {
  font-size: 0.95rem;
}

.message.bot .prose {
  color: #1a1a1a;
}

.message.user .prose {
  color: white;
}

/* Style links in bot messages */
.message.bot a {
  color: #2e3192;
  text-decoration: underline;
}

/* Style code blocks in messages */
.message pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
  overflow-x: auto;
}

.message.user pre {
  background: rgba(255, 255, 255, 0.1);
}

/* Message timestamp or metadata */
.message-meta {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  opacity: 0.7;
}

/* Typing indicator for bot */
.typing-indicator {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f0f2f5;
  border-radius: 1rem;
  width: fit-content;
}

.typing-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #2e3192;
  border-radius: 50%;
  animation: typingBounce 1s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingBounce {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* Message groups spacing */
.message + .message {
  margin-top: 0.25rem;
}

@media (max-width: 768px) {
  .main-heading {
    font-size: 0.875rem; /* Decreased from 1rem */
    line-height: 1.3;
    margin-bottom: 1.5rem;
  }

  .subtitle {
    font-size: 0.875rem;
  }

  .chat-container {
    margin: 0.5rem;
    height: calc(100vh - 320px); /* Decreased from 220px */
  }

  .messages {
    max-height: calc(100vh - 420px); /* Decreased from 320px */
  }

  .form-container {
    margin: 0.35rem;
    padding: 0.75rem 0.5rem 1.25rem;
    max-width: 900px; /* Increased from 800px for tablet view */
  }

  .content-wrapper {
    padding: 0.5rem 0.35rem;
  }
  .chat-interface-container {
    height: calc(100vh - 180px);
    margin-top: 1.5rem;
  }

  .content-wrapper {
    padding-top: 6rem;
  }
}

@media (max-width: 480px) {
  .main-heading {
    font-size: 0.75rem; /* Decreased from 0.875rem */
    line-height: 1.4;
    margin-bottom: 1rem;
  }
}

@media (max-height: 800px) {
  .chat-interface-container {
    height: calc(100vh - 160px);
    margin-top: 1rem;
  }

  .content-wrapper {
    padding-top: 5rem;
  }
}

/* Add simple fade animations */
.transition-opacity {
  transition-property: opacity;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Add smooth scrolling for chat container */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Remove any transform animations to keep it simple */
.form-container,
.chat-container {
  will-change: opacity;
}

/* Add these new animations */
@keyframes slideRight {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Welcome page animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  20% {
    opacity: 0.5;
  }
  80% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(-100px) scale(1);
    opacity: 0;
  }
}

@keyframes floatIcon {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes techFloat {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-30px) scale(1.1);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

@keyframes gradientPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
}

/* Main page animations */
.animate-fadeInUp {
  animation: fadeInUp 1s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 1s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 1s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 1s ease-out forwards;
}

.animate-float {
  animation: float 6s infinite ease-in-out;
}

.animate-messageAppear {
  animation: messageAppear 0.5s forwards;
}

/* Welcome page styles */
.welcome-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: #1b1464;
}

.welcome-content {
  text-align: center;
  z-index: 2;
  animation: fadeInUp 1s ease;
  background: rgba(255, 255, 255, 0.05);
  padding: 2.5rem;
  border-radius: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  max-width: 28rem;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  opacity: 0.5;
  animation: particleFloat 15s infinite linear;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
}

.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.enter-btn {
  transition: all 0.3s ease;
}

.enter-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Add gradient utilities */
.bg-gradient-radial {
  background-image: radial-gradient(
    circle at center,
    var(--tw-gradient-from) 0%,
    var(--tw-gradient-to) 100%
  );
}

/* Enhance text shadow for better visibility */
.text-shadow-elegant {
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Smooth animation utilities */
.transition-transform {
  transition-property: transform;
}

.transition-all {
  transition-property: all;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse {
  animation: pulse 2s infinite ease-in-out;
}

/* Main page styles */
.main-container {
  display: flex;
  height: 100vh;
  background: #1b1464;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  margin: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scroll-behavior: smooth;
}

.message {
  max-width: 80%;
  margin: 0.5rem 0;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  line-height: 1.4;
  animation: fadeIn 0.3s ease-in-out;
}

.message.user {
  margin-left: auto;
  background-color: #2e3192;
  color: white;
  border-top-right-radius: 0.25rem;
}

.message.bot {
  margin-right: auto;
  background-color: #f0f2f5;
  color: #1a1a1a;
  border-top-left-radius: 0.25rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bg-blur {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Status indicators */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}

.status-indicator.connected {
  background-color: #22c55e;
}

.status-indicator.disconnected {
  background-color: #ef4444;
}
