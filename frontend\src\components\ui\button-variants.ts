import { cva } from 'class-variance-authority';

export const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground px-3 sm:px-4 py-1.5 sm:py-2 bg-blue-800 text-white rounded-md hover:bg-blue-700 active:scale-95 transition-all',
        destructive:
          'bg-destructive text-destructive-foreground px-3 sm:px-4 py-1.5 sm:py-2 bg-orange-800 text-white rounded-md hover:bg-orange-700 active:scale-95 transition-all',
        outline:
          'border border-input bg-background hover:bg-accent hover:text-accent-foreground active:scale-95 transition-all',
        secondary:
          'bg-secondary text-secondary-foreground px-2 sm:px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs sm:text-sm hover:bg-gray-300 active:scale-95 transition-all',
        ghost: 'hover:bg-accent hover:text-accent-foreground active:scale-95 transition-all',
        link: 'text-primary underline-offset-4 hover:underline active:scale-95 transition-all',
        icon: 'hover:bg-accent hover:text-accent-foreground active:scale-95 transition-all',
      },
      size: {
        default: 'h-8 sm:h-10 px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base',
        sm: 'h-7 sm:h-9 rounded-md px-2 sm:px-3 text-xs sm:text-sm',
        lg: 'h-9 sm:h-11 rounded-md px-6 sm:px-8 text-base sm:text-lg',
        icon: 'h-8 w-8 sm:h-10 sm:w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);
