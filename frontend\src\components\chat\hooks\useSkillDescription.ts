import { useState } from 'react';
import { DESCRIPTION_MESSAGES, LOG_MESSAGES } from '@/components/chat/utils/constants';
import { fallbackDescriptions } from '@/components/chat/utils/queryUtils';
import axiosInstance from '@/axios/axiosInstance';

interface UseSkillDescriptionReturn {
  generateDescription: (topic: string) => Promise<string>;
  isLoading: boolean;
  error: Error | null;
}

export const useSkillDescription = (): UseSkillDescriptionReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Generate a description for a skill, role, or technology
  const getDescription = async (topic: string) => {
    try {
      // First try to get a description from the backend
      const response = await axiosInstance.post('/api/openai-intent', {
        query: `Generate a concise description of ${topic}`,
      });

      // If we have a response, extract a description
      if (response.data && response.data.intent) {
        return { description: response.data.intent };
      }

      // Check if we have a fallback for this topic
      for (const key in fallbackDescriptions) {
        if (topic.toLowerCase().includes(key)) {
          return { description: fallbackDescriptions[key] };
        }
      }

      // Generic fallback
      return {
        description: `${topic} is an important skill in the technology industry, valued by employers and essential for career growth.`,
      };
    } catch (error) {
      setError(error as Error);
      console.error(LOG_MESSAGES.FAILED_DESCRIPTION, error);
      return {
        description: `${topic} is an important skill in the technology industry, valued by employers and essential for career growth.`,
      };
    } finally {
      setIsLoading(false);
    }
  };

  const generateDescription = async (topic: string): Promise<string> => {
    setIsLoading(true);
    setError(null);
    const response = await getDescription(topic);
    return response.description || DESCRIPTION_MESSAGES.FALLBACK.replace('{topic}', topic);
  };

  return {
    generateDescription,
    isLoading,
    error,
  };
};
