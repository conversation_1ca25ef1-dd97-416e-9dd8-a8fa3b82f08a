# Mentoring Agent

## Project Overview
The Mentoring Agent is a web application designed to recommend the best suitable course and learning path based on the user interest and skill level. It features a chat interface where users can interact and receive course suggestions.

## Prerequisites
- Node.js and npm installed on your machine.
- Python and pip installed for any Python scripts.

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd mentoring-agent
```

### 2. Frontend Setup
1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```
2. Install the dependencies:
   ```bash
   npm install
   ```
3. Start the frontend server:
   ```bash
   npm run dev
   ```
   The frontend will be running at `http://localhost:5173`.

### 3. Backend Setup
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```
2. Install the dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Set up environment variables:
   - Create a `.env` file in the backend directory.
   - Refer to the `.env.example` file for the required environment variables and format.

4. Start the backend server:
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 3000 --reload
   ```
   The backend will be running at `http://localhost:3000`.

## API Keys
- API keys are required for certain functionalities.
- Please request the necessary API keys from your manager by raising a request.

## Usage
- Open your browser and navigate to `http://localhost:5173`.
- Interact with the chat interface to receive course recommendations.

## Troubleshooting
- Ensure all dependencies are installed correctly.
- Check the console for any error messages and resolve them accordingly.
- Verify that the backend server is running and accessible.

## Contributing
- Fork the repository and create a new branch for your feature or bug fix.
- Submit a pull request with a detailed description of your changes.