import { memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const WelcomeCard = memo(() => {
  const navigate = useNavigate();

  const handleEnterClick = () => {
    navigate('/main');
  };

  return (
    <Card className="welcome-content z-10 text-center animate-fadeInUp bg-white/30 backdrop-blur-sm border-none shadow-lg w-[90%] sm:w-[80%] md:w-[70%] lg:w-[500px] mx-auto">
      <CardHeader className="px-1 sm:px-1.5 py-1 sm:py-1.5 space-y-0">
        <CardTitle className="text-3xl sm:text-4xl md:text-5xl font-bold text-white animate-slideDown whitespace-nowrap">
          Mentoring Agent
        </CardTitle>
        <CardDescription className="text-lg sm:text-xl text-white animate-slideUp">
          Your AI-powered career guide awaits
        </CardDescription>
      </CardHeader>
      <CardContent className="px-1 sm:px-1.5 pb-1 sm:pb-1.5">
        <Button
          onClick={handleEnterClick}
          className="enter-btn w-full sm:w-auto px-2 sm:px-3 py-0.5 text-lg sm:text-xl bg-blue-100 text-blue-800 rounded-full hover:bg-blue-50 hover:shadow-lg transition-all animate-fadeIn"
          variant="secondary"
          size="lg"
        >
          Enter
        </Button>
      </CardContent>
    </Card>
  );
});

WelcomeCard.displayName = 'WelcomeCard';

export default WelcomeCard;
