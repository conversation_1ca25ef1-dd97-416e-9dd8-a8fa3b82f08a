import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import { getToken, saveToken, removeToken } from '../lib/tokenStorage';
import { ERROR_MESSAGES } from '@/components/chat/utils/constants';

// Define custom error type
interface ApiError extends Error {
  response?: {
    data: {
      message?: string;
      error?: string;
    };
    status?: number;
  };
}

// Extend InternalAxiosRequestConfig to include _retry property
interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// Create a separate axios instance for auth requests to avoid interceptors
const authAxios = axios.create({
  baseURL: import.meta.env.VITE_MENTORING_AGENT_API_URL || 'http://localhost:3000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
// Store pending requests
let failedQueue: Array<{
  resolve: (value?: unknown) => void;
  reject: (reason?: Error) => void;
}> = [];

const processQueue = (error: Error | null, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

// Create an axios instance with default config
const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_MENTORING_AGENT_API_URL || 'http://localhost:3000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = getToken();
    const language = localStorage.getItem('language') || 'en';

    if (accessToken) {
      config.headers['Authorization'] = `Bearer ${accessToken}`;
    }
    config.headers['Accept-Language'] = language;
    return config;
  },
  (error: AxiosError) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const apiError = error as ApiError;
    const originalRequest = error.config as CustomAxiosRequestConfig;

    // Handle specific error cases
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout:', error);
      return Promise.reject(new Error(ERROR_MESSAGES.BACKEND_CONNECTION));
    }

    if (!error.response) {
      console.error('Network error:', error);
      return Promise.reject(new Error(ERROR_MESSAGES.BACKEND_CONNECTION));
    }

    // Handle token refresh for 401 errors
    if (error.response.status === 401 && originalRequest) {
      if (isRefreshing) {
        // If refresh is in progress, add request to queue
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            if (originalRequest.headers) {
              originalRequest.headers['Authorization'] = `Bearer ${token}`;
            }
            return axiosInstance(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Attempt to refresh the token
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await authAxios.post('/auth/refresh', {
          refresh_token: refreshToken,
        });

        const { access_token } = response.data;
        saveToken(access_token);

        // Update the original request with new token
        if (originalRequest.headers) {
          originalRequest.headers['Authorization'] = `Bearer ${access_token}`;
        }

        // Process queued requests
        processQueue(null, access_token);

        // Retry the original request
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError as Error, null);
        removeToken();
        localStorage.removeItem('refresh_token');
        return Promise.reject(new Error('Session expired. Please login again.'));
      } finally {
        isRefreshing = false;
      }
    }

    // Handle other HTTP status codes
    switch (error.response.status) {
      case 403:
        console.error('Forbidden access:', error);
        return Promise.reject(new Error('Access forbidden'));
      case 404:
        console.error('Resource not found:', error);
        return Promise.reject(new Error('Resource not found'));
      case 500:
        console.error('Server error:', error);
        return Promise.reject(new Error(ERROR_MESSAGES.BACKEND_ERROR));
      default: {
        const errorMessage =
          apiError.response?.data?.message ||
          apiError.response?.data?.error ||
          ERROR_MESSAGES.PROCESSING_ERROR;
        console.error('API error:', error);
        return Promise.reject(new Error(errorMessage));
      }
    }
  }
);

export default axiosInstance;
