import * as React from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle } from 'lucide-react';

interface SuccessMessageProps extends React.HTMLAttributes<HTMLDivElement> {
  message: string;
}

const SuccessMessage = React.forwardRef<HTMLDivElement, SuccessMessageProps>(
  ({ className, message, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded animate-pulse transition-all duration-300 ease-in-out',
          className
        )}
        {...props}
      >
        <p className="text-sm font-medium flex items-center">
          <CheckCircle className="h-4 w-4 mr-2" />
          {message}
        </p>
      </div>
    );
  }
);
SuccessMessage.displayName = 'SuccessMessage';

export { SuccessMessage };
