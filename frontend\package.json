{"name": "mentoring-agent", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-slot": "^1.2.2", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "lucide-react": "^0.511.0", "pg": "^8.13.3", "pgvector": "^0.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.0.0", "react-router-dom": "^7.5.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^15.9.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "shadcn": "^2.5.0", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "vite": "^6.3.5"}}